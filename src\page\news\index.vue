<template>
  <!-- center -->
  <div class="center-container container">
    <leftNav v-if="!isEn" :nav="navData" @nav-back="navBack"></leftNav>
    <div class="center_content col-sm-8" :class="isEn ? 'col-md-12' : 'col-md-9'">
      <breadcrumb :breadcrumb="breadcrumbData"></breadcrumb>
      <!-- 新闻列表 -->
      <div v-for="(list, index) in newsList" :key="index" class="newslist_item animated" data-ani="fadeInUp" :data-delay="100 * index">
        <a class="media" @click="show_detail(list)">
          <div class="media-left news_img">
            <img :src="list.src" class="media-object" alt="">
          </div>
          <div class="media-body">
            <p class="media-heading news_title">{{ list.title }}</p>
            <p class="news-content">{{ list.content }}</p>
            <p class="news_time">{{ list.time }}</p>
          </div>
        </a>
      </div>
      <Page :total="total" :page-size="listQuery.pageSize" :current="listQuery.pageNumber" show-total :styles="{clear: 'both',margin:'16px -10px 0 0',textAlign: 'right'}" @on-change='handleCurrentChange'/>
    </div>
  </div>
</template>

<script>
import config from '@/api/config'
import { getCurList } from '@/api/api'
import leftNav from "@/components/leftNav"
import breadcrumb from "@/components/breadcrumbComponent"
import layoutGlobal from '@/assets/js/layoutGlobal.js' // 全局共用布局组件
import animateGlobal from '@/assets/js/animateGlobal.js'
export default {
  mixins: [layoutGlobal, animateGlobal],
  name: "news",
  components: {
    leftNav,
    breadcrumb
  },
  data() {
    return {
      curIndex: 1,
      total: 1,
      listQuery: {
        pageSize: 8,
        pageNumber: 1,
        category_code: 10003001
      },
      newsList: [],
      breadcrumbData: [
        {
            name: '新闻中心',
            enName: 'News',
            path: '/news'
        }
      ], // 面包屑数据
      navData: [
        {
          title: "新闻中心",
          entitle: "NEWS",
          name: "公司动态",
          enName: "Company News",
          path: "/news?id=1",
          icon: "md-document"
        },
        {
          title: "新闻中心",
          name: "基层信息",
          enName: "Grassroots Information",
          path: "/news?id=2",
          icon: "md-chatbubbles"
        },
        {
          title: "新闻中心",
          name: "行业资讯",
          enName: "Industry information",
          path: "/news?id=3",
          icon: "md-heart"
        }
      ]
    };
  },
  methods: {
    // 左侧栏回调
    navBack (d) {
      this.curIndex = d.id
      this.listQuery.pageNumber = 1
      this.getList()
    },
    // 显示详情页
    show_detail (list) {
      let queryId = this.$route.query.id || '1'
      window.localStorage.setItem('newsObj', JSON.stringify(list))
      // 在新窗口打开详情页
      const detailUrl = `${window.location.origin}/newsDetail?id=${queryId}&detailId=${list.id}`
      window.open(detailUrl, '_blank')
    },
    // 获取列表数据
    getList () {
      let baseImgUrl = process.env.NODE_ENV === 'development' ? config.imgUrl.dev : config.imgUrl.pro
      this.newsList = []
      switch(this.curIndex) {
        case '1':
          if(this.isEn) {
            this.listQuery.category_code = 20003001
          } else {
            this.listQuery.category_code = 10003001
          }
          break
        case '2':
          if(this.isEn) {
            this.listQuery.category_code = 20003002
          } else {
            this.listQuery.category_code = 10003002
          }
          break
        case '3':
          if(this.isEn) {
            this.listQuery.category_code = 20003003
          } else {
            this.listQuery.category_code = 10003003
          }
          break
        default:
          if(this.isEn) {
            this.listQuery.category_code = 20003001
          } else {
            this.listQuery.category_code = 10003001
          }
      }
      getCurList(this.listQuery).then(res => {
        if(res.data.Code === 10000) {
          this.total = res.data.total
          res.data.rows.map(item => {
            this.newsList.push({
              id: item.id,
              src: baseImgUrl + item.article_img,
              content: item.article_remark,
              content_detail: item.article_content,
              title: item.article_title,
              time: item.article_pubdate.split(' ')[0]
            })
          })
          this.$nextTick(() => {
            this.handleAnimate()
          })
        } else{
          this.$Message.error(res.data.Message)
        }
      })
    },
    handleCurrentChange (val) {
      this.listQuery.pageNumber = val
      this.getList()
    }
  },
  computed: {
    isEn () {
      return this.$store.state.isEn
    }
  },
  watch: {
    isEn (newVal, oldVal) {
      this.getList()
      // 设置banner图片
      this.bannerArr = [
        {
          src: this.isEn ? "static/images/banner/newbanner_en.jpg" : "static/images/banner/newbanner.png"
        }
      ]
      this.$store.commit("setBannerObj", this.bannerArr)
    }
  },
  created() {
    // 设置banner图片
    let bannerArr = [
      {
        src: this.isEn ? "static/images/banner/newbanner_en.jpg" : "static/images/banner/newbanner.png"
      }
    ];
    this.$store.commit("setBannerObj", bannerArr)
  }
};
</script>
<style scoped>
.center-container {
  display: flex;
}
.news_img {
  width: 30%;
  float: left;
  max-height: 155px;
  padding-right: 10px;
  overflow: hidden;
  display: flex;
}
.news_img img {
  width: 100%;
  max-height: 100%;
}
.news_title {
  font-size: 18px;
  font-weight: bold;
  color: #000;
  margin-bottom: 9px;
  -ms-word-break: break-all;
  word-break: break-all;
  width: 100%;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.news-content {
  font-size: 14px;
  color: #4C4C4C;
  -ms-word-break: break-all;
  word-break: break-all;
  width: 100%;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.newslist_item {
  position: relative;
  padding: 10px;
  margin-bottom: 15px;
}
.newslist_item .media {
  display: block;
  overflow: hidden;
}
.newslist_item .media::before {
  z-index: 0;
  content: '';
  position: absolute;
  background: #1981E6;
  left: 0%;
  bottom: 0;
  width: 0%;
  height: 1px;
  transition: .2s all linear;
}
.newslist_item .media:hover::before {
  left: 0;
  width: 100%;
}
.news_time {
  color:#1981E6;
  vertical-align: bottom;
  /* position: absolute;
  bottom: 0; */
}
@media only screen and (max-width: 767px) {
  .news-content {
    line-height: 21px;
    -webkit-line-clamp: 2;
  }
  .news_title[data-v-48169766] {
    font-size: 16px;
    margin-bottom: 5px;
  }
}
@media only screen and (max-width: 767px) and (min-width: 481px) {
  .news_time {
    display: none;
  }
}
@media only screen and (max-width: 480px) {
  .news_img { 
    width: 100%;
    display: block;
    margin: 0 0 10px;
  }
  .newslist_item .media-body {
    display: block;
    width: 100%;
  }
  .news_time {
    position: static;
  }
}
</style>
