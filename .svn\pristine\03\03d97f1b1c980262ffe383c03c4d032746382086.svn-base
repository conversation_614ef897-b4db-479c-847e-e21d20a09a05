!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("vue")):"function"==typeof define&&define.amd?define("iview/locale",["vue"],t):"object"==typeof exports?exports["iview/locale"]=t(require("vue")):e["iview/locale"]=t(e.Vue)}("undefined"!=typeof self?self:this,function(e){return function(e){var t={};function o(n){if(t[n])return t[n].exports;var r=t[n]={i:n,l:!1,exports:{}};return e[n].call(r.exports,r,r.exports,o),r.l=!0,r.exports}return o.m=e,o.c=t,o.d=function(e,t,n){o.o(e,t)||Object.defineProperty(e,t,{configurable:!1,enumerable:!0,get:n})},o.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return o.d(t,"a",t),t},o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},o.p="/dist/locale/",o(o.s=13)}({0:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){n||void 0!==window.iview&&("langs"in iview||(iview.langs={}),iview.langs[e.i.locale]=e)};var n=function(e){return e&&e.__esModule?e:{default:e}}(o(1)).default.prototype.$isServer},1:function(t,o){t.exports=e},13:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n={i:{locale:"hi-IN",select:{placeholder:"कृपया चुने|",noMatch:"कोई आकड़ा उपलब्ध नहीं है|",loading:"लोड हो रहा है"},table:{noDataText:"कोई आकड़ा उपलब्ध नहीं है",noFilteredDataText:"कोई आकड़ा उपलब्ध नहीं है",confirmFilter:"पुष्टि करें",resetFilter:"पुनः तैयार करना",clearFilter:"सब कुछ"},datepicker:{selectDate:"दिनांक चुनें",selectTime:"समय चुनें",startTime:"प्रारंभ समय",endTime:"समाप्ति समय",clear:"साफ़ करें",ok:"ठीक",datePanelLabel:"[mmmm] [yyyy]",month:"महीना",month1:"जनवरी",month2:"फरवरी",month3:"मार्च",month4:"अप्रैल",month5:"मई",month6:"जून",month7:"जुलाई",month8:"अगस्त",month9:"सितंबर",month10:"अक्टूबर",month11:"नवंबर",month12:"दिसंबर",year:"साल",weekStartDay:"0",weeks:{sun:"रविवार",mon:"सोमवार",tue:"मंगलवार",wed:"बुधवार",thu:"गुरुवार",fri:"शुक्रवार",sat:"शनिवार"},months:{m1:"जनवरी",m2:"फरवरी",m3:"मार्च",m4:"अप्रैल",m5:"मई",m6:"जून",m7:"जुलाई",m8:"अगस्त",m9:"सितंबर",m10:"अक्टूबर",m11:"नवंबर",m12:"दिसंबर"}},transfer:{titles:{source:"स्रोत",target:"लक्ष्य"},filterPlaceholder:"यहां खोजें",notFoundText:"कोई आकड़ा उपलब्ध नहीं है"},modal:{okText:"ठीक",cancelText:"निरस्त करना"},poptip:{okText:"ठीक",cancelText:"निरस्त करना"},page:{prev:"पिछला पेज",next:"अगला पेज",total:"समस्त",item:"एक चीज",items:"अनेक चीज",prev5:"पिछला 5 पेज",next5:"अगला 5 पेज",page:"/page",goto:"जाओ",p:""},rate:{star:"प्रसिद्ध",stars:"प्रसिद्ध"},tree:{emptyText:"कोई आकड़ा उपलब्ध नहीं है"}}};(0,function(e){return e&&e.__esModule?e:{default:e}}(o(0)).default)(n),t.default=n}})});
//# sourceMappingURL=hi-IN.js.map