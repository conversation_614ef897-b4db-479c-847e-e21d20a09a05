{"_args": [["postcss-normalize-unicode@4.0.1", "F:\\xt_shipping"]], "_development": true, "_from": "postcss-normalize-unicode@4.0.1", "_id": "postcss-normalize-unicode@4.0.1", "_inBundle": false, "_integrity": "sha512-od18Uq2wCYn+vZ/qCOeutvHjB5jm57ToxRaMeNuf0nWVHaP9Hua56QyMF6fs/4FSUnVIw0CBPsU0K4LnBPwYwg==", "_location": "/postcss-normalize-unicode", "_phantomChildren": {"caniuse-lite": "1.0.30000987", "chalk": "2.4.2", "electron-to-chromium": "1.3.204", "has-flag": "3.0.0", "node-releases": "1.1.26", "source-map": "0.6.1"}, "_requested": {"type": "version", "registry": true, "raw": "postcss-normalize-unicode@4.0.1", "name": "postcss-normalize-unicode", "escapedName": "postcss-normalize-unicode", "rawSpec": "4.0.1", "saveSpec": null, "fetchSpec": "4.0.1"}, "_requiredBy": ["/cssnano-preset-default"], "_resolved": "https://registry.npmjs.org/postcss-normalize-unicode/-/postcss-normalize-unicode-4.0.1.tgz", "_spec": "4.0.1", "_where": "F:\\xt_shipping", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "dependencies": {"browserslist": "^4.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "description": "Normalize unicode-range descriptors, and can convert to wildcard ranges.", "devDependencies": {"babel-cli": "^6.0.0", "cross-env": "^5.0.0"}, "engines": {"node": ">=6.9.0"}, "files": ["dist", "LICENSE-MIT"], "homepage": "https://github.com/cssnano/cssnano", "keywords": ["css", "postcss", "postcss-plugin"], "license": "MIT", "main": "dist/index.js", "name": "postcss-normalize-unicode", "repository": {"type": "git", "url": "git+https://github.com/cssnano/cssnano.git"}, "scripts": {"prepublish": "cross-env BABEL_ENV=publish babel src --out-dir dist --ignore /__tests__/"}, "version": "4.0.1"}