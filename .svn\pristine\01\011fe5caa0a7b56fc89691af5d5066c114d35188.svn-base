{"_args": [["fast-deep-equal@2.0.1", "F:\\xt_shipping"]], "_development": true, "_from": "fast-deep-equal@2.0.1", "_id": "fast-deep-equal@2.0.1", "_inBundle": false, "_integrity": "sha1-ewUhjd+WZ79/Nwv3/bLLFf3Qqkk=", "_location": "/@vue/cli-plugin-eslint/fast-deep-equal", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "fast-deep-equal@2.0.1", "name": "fast-deep-equal", "escapedName": "fast-deep-equal", "rawSpec": "2.0.1", "saveSpec": null, "fetchSpec": "2.0.1"}, "_requiredBy": ["/@vue/cli-plugin-eslint/ajv"], "_resolved": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-2.0.1.tgz", "_spec": "2.0.1", "_where": "F:\\xt_shipping", "author": {"name": "<PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/epoberezkin/fast-deep-equal/issues"}, "description": "Fast deep equal", "devDependencies": {"benchmark": "^2.1.4", "coveralls": "^2.13.1", "deep-eql": "latest", "deep-equal": "latest", "eslint": "^4.0.0", "lodash": "latest", "mocha": "^3.4.2", "nano-equal": "latest", "nyc": "^11.0.2", "pre-commit": "^1.2.2", "ramda": "latest", "shallow-equal-fuzzy": "latest", "typescript": "^2.6.1", "underscore": "latest"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/epoberezkin/fast-deep-equal#readme", "keywords": ["fast", "equal", "deep-equal"], "license": "MIT", "main": "index.js", "name": "fast-deep-equal", "nyc": {"exclude": ["**/spec/**", "node_modules"], "reporter": ["lcov", "text-summary"]}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/fast-deep-equal.git"}, "scripts": {"eslint": "eslint *.js benchmark spec", "test": "npm run eslint && npm run test-ts && npm run test-cov", "test-cov": "nyc npm run test-spec", "test-spec": "mocha spec/*.spec.js -R spec", "test-ts": "tsc --target ES5 --noImplicitAny index.d.ts"}, "types": "index.d.ts", "version": "2.0.1"}