#!/usr/bin/env node

/**
 * 检查构建环境脚本
 * 验证构建所需的依赖和配置
 */

'use strict'

const { execSync } = require('child_process')
const chalk = require('chalk')
const fs = require('fs')
const path = require('path')

class EnvironmentChecker {
  constructor() {
    this.checks = []
  }

  // 检查Node.js版本
  checkNodeVersion() {
    try {
      const nodeVersion = process.version
      const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0])
      
      if (majorVersion >= 8) {
        this.checks.push({
          name: 'Node.js版本',
          status: 'pass',
          message: `${nodeVersion} ✓`
        })
      } else {
        this.checks.push({
          name: 'Node.js版本',
          status: 'fail',
          message: `${nodeVersion} (需要 >= 8.0.0)`
        })
      }
    } catch (error) {
      this.checks.push({
        name: 'Node.js版本',
        status: 'fail',
        message: '无法检测Node.js版本'
      })
    }
  }

  // 检查npm版本
  checkNpmVersion() {
    try {
      const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim()
      this.checks.push({
        name: 'npm版本',
        status: 'pass',
        message: `${npmVersion} ✓`
      })
    } catch (error) {
      this.checks.push({
        name: 'npm版本',
        status: 'fail',
        message: '无法检测npm版本'
      })
    }
  }

  // 检查puppeteer是否已安装
  checkPuppeteer() {
    try {
      const packageJson = require('../package.json')
      if (packageJson.dependencies && packageJson.dependencies.puppeteer) {
        this.checks.push({
          name: 'Puppeteer依赖',
          status: 'pass',
          message: `${packageJson.dependencies.puppeteer} ✓`
        })
      } else {
        this.checks.push({
          name: 'Puppeteer依赖',
          status: 'warning',
          message: '未安装，构建时会自动安装'
        })
      }
    } catch (error) {
      this.checks.push({
        name: 'Puppeteer依赖',
        status: 'fail',
        message: '无法检查package.json'
      })
    }
  }

  // 检查nrm是否安装
  checkNrm() {
    try {
      execSync('nrm --version', { stdio: 'ignore' })
      this.checks.push({
        name: 'nrm工具',
        status: 'pass',
        message: '已安装 ✓'
      })
    } catch (error) {
      this.checks.push({
        name: 'nrm工具',
        status: 'warning',
        message: '未安装，将使用npm config切换镜像'
      })
    }
  }

  // 检查当前npm镜像
  checkNpmRegistry() {
    try {
      const registry = execSync('npm config get registry', { encoding: 'utf8' }).trim()
      this.checks.push({
        name: '当前npm镜像',
        status: 'info',
        message: registry
      })
    } catch (error) {
      this.checks.push({
        name: '当前npm镜像',
        status: 'fail',
        message: '无法获取当前镜像'
      })
    }
  }

  // 检查构建脚本是否存在
  checkBuildScripts() {
    const scripts = [
      'scripts/build-with-prerender.js',
      'build-prerender.bat'
    ]

    scripts.forEach(script => {
      if (fs.existsSync(path.join(__dirname, '..', script))) {
        this.checks.push({
          name: `构建脚本 ${script}`,
          status: 'pass',
          message: '存在 ✓'
        })
      } else {
        this.checks.push({
          name: `构建脚本 ${script}`,
          status: 'fail',
          message: '不存在'
        })
      }
    })
  }

  // 检查webpack配置
  checkWebpackConfig() {
    const configPath = path.join(__dirname, '..', 'build', 'webpack.prod.conf.js')
    if (fs.existsSync(configPath)) {
      try {
        const config = fs.readFileSync(configPath, 'utf8')
        if (config.includes('PrerenderSPAPlugin')) {
          this.checks.push({
            name: 'Webpack预渲染配置',
            status: 'pass',
            message: '已配置 ✓'
          })
        } else {
          this.checks.push({
            name: 'Webpack预渲染配置',
            status: 'fail',
            message: '未找到PrerenderSPAPlugin配置'
          })
        }
      } catch (error) {
        this.checks.push({
          name: 'Webpack预渲染配置',
          status: 'fail',
          message: '无法读取配置文件'
        })
      }
    } else {
      this.checks.push({
        name: 'Webpack预渲染配置',
        status: 'fail',
        message: 'webpack.prod.conf.js不存在'
      })
    }
  }

  // 显示检查结果
  displayResults() {
    console.log(chalk.blue('\n🔍 构建环境检查结果\n'))
    console.log('='.repeat(50))

    let passCount = 0
    let failCount = 0
    let warningCount = 0

    this.checks.forEach(check => {
      let icon, color
      switch (check.status) {
        case 'pass':
          icon = '✅'
          color = chalk.green
          passCount++
          break
        case 'fail':
          icon = '❌'
          color = chalk.red
          failCount++
          break
        case 'warning':
          icon = '⚠️ '
          color = chalk.yellow
          warningCount++
          break
        case 'info':
          icon = 'ℹ️ '
          color = chalk.blue
          break
        default:
          icon = '❓'
          color = chalk.gray
      }

      console.log(`${icon} ${color(check.name)}: ${check.message}`)
    })

    console.log('='.repeat(50))
    console.log(chalk.green(`✅ 通过: ${passCount}`))
    if (warningCount > 0) console.log(chalk.yellow(`⚠️  警告: ${warningCount}`))
    if (failCount > 0) console.log(chalk.red(`❌ 失败: ${failCount}`))

    if (failCount === 0) {
      console.log(chalk.green('\n🎉 环境检查通过！可以开始构建。'))
      console.log(chalk.blue('\n运行构建命令:'))
      console.log(chalk.cyan('  npm run build:optimized'))
      console.log(chalk.cyan('  或'))
      console.log(chalk.cyan('  npm run build:prerender'))
    } else {
      console.log(chalk.red('\n❌ 环境检查发现问题，请先解决上述问题。'))
    }
  }

  // 执行所有检查
  runAllChecks() {
    console.log(chalk.blue('🚀 开始检查构建环境...\n'))

    this.checkNodeVersion()
    this.checkNpmVersion()
    this.checkPuppeteer()
    this.checkNrm()
    this.checkNpmRegistry()
    this.checkBuildScripts()
    this.checkWebpackConfig()

    this.displayResults()
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const checker = new EnvironmentChecker()
  checker.runAllChecks()
}

module.exports = EnvironmentChecker
