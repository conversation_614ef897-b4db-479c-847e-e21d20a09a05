{"_args": [["iferr@0.1.5", "F:\\xt_shipping"]], "_development": true, "_from": "iferr@0.1.5", "_id": "iferr@0.1.5", "_inBundle": false, "_integrity": "sha1-xg7taebY/bazEEofy8ocGS3FtQE=", "_location": "/iferr", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "iferr@0.1.5", "name": "iferr", "escapedName": "iferr", "rawSpec": "0.1.5", "saveSpec": null, "fetchSpec": "0.1.5"}, "_requiredBy": ["/copy-concurrently", "/fs-write-stream-atomic"], "_resolved": "https://registry.npmjs.org/iferr/-/iferr-0.1.5.tgz", "_spec": "0.1.5", "_where": "F:\\xt_shipping", "author": {"name": "Nadav I<PERSON>gi"}, "bugs": {"url": "https://github.com/shesek/iferr/issues"}, "description": "Higher-order functions for easier error handling", "devDependencies": {"coffee-script": "^1.7.1", "mocha": "^1.18.2"}, "homepage": "https://github.com/shesek/iferr", "keywords": ["error", "errors"], "license": "MIT", "main": "index.js", "name": "iferr", "repository": {"type": "git", "url": "git+https://github.com/shesek/iferr.git"}, "scripts": {"prepublish": "coffee -c index.coffee", "test": "mocha"}, "version": "0.1.5"}