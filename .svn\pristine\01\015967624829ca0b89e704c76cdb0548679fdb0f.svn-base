{"_args": [["big.js@5.2.2", "F:\\xt_shipping"]], "_development": true, "_from": "big.js@5.2.2", "_id": "big.js@5.2.2", "_inBundle": false, "_integrity": "sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ==", "_location": "/big.js", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "big.js@5.2.2", "name": "big.js", "escapedName": "big.js", "rawSpec": "5.2.2", "saveSpec": null, "fetchSpec": "5.2.2"}, "_requiredBy": ["/loader-utils"], "_resolved": "https://registry.npmjs.org/big.js/-/big.js-5.2.2.tgz", "_spec": "5.2.2", "_where": "F:\\xt_shipping", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "browser": "big.js", "bugs": {"url": "https://github.com/MikeMcl/big.js/issues"}, "collective": {"type": "opencollective", "url": "https://opencollective.com/bigjs"}, "description": "A small, fast, easy-to-use library for arbitrary-precision decimal arithmetic", "engines": {"node": "*"}, "files": ["big.js", "big.mjs", "big.min.js"], "homepage": "https://github.com/MikeMcl/big.js#readme", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "license": "MIT", "main": "big", "module": "big.mjs", "name": "big.js", "repository": {"type": "git", "url": "git+https://github.com/MikeMcl/big.js.git"}, "scripts": {"build": "uglifyjs big.js --source-map -c -m -o big.min.js", "test": "node ./test/every-test.js"}, "version": "5.2.2"}