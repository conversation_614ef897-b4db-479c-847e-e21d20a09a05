{"_args": [["utila@0.4.0", "F:\\xt_shipping"]], "_development": true, "_from": "utila@0.4.0", "_id": "utila@0.4.0", "_inBundle": false, "_integrity": "sha1-ihagXURWV6Oupe7MWxKk+lN5dyw=", "_location": "/utila", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "utila@0.4.0", "name": "utila", "escapedName": "utila", "rawSpec": "0.4.0", "saveSpec": null, "fetchSpec": "0.4.0"}, "_requiredBy": ["/dom-converter", "/pretty-error", "/renderkid"], "_resolved": "https://registry.npmjs.org/utila/-/utila-0.4.0.tgz", "_spec": "0.4.0", "_where": "F:\\xt_shipping", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/AriaMinaei/utila/issues"}, "description": "notareplacementforunderscore", "devDependencies": {"coffee-script": "~1.6.3", "little-popo": "~0.1"}, "homepage": "https://github.com/AriaMinaei/utila#readme", "keywords": ["utilities"], "license": "MIT", "main": "lib/utila.js", "name": "utila", "repository": {"type": "git", "url": "git+https://github.com/AriaMinaei/utila.git"}, "scripts": {"prepublish": "coffee --bare --compile --output ./lib ./src"}, "version": "0.4.0"}