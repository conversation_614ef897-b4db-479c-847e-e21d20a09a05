{"_args": [["color@3.1.2", "F:\\xt_shipping"]], "_development": true, "_from": "color@3.1.2", "_id": "color@3.1.2", "_inBundle": false, "_integrity": "sha512-vXTJhHebByxZn3lDvDJYw4lR5+uB3vuoHsuYA5AKuxRVn5wzzIfQKGLBmgdVRHKTJYeK5rvJcHnrd0Li49CFpg==", "_location": "/cssnano-preset-default/color", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "color@3.1.2", "name": "color", "escapedName": "color", "rawSpec": "3.1.2", "saveSpec": null, "fetchSpec": "3.1.2"}, "_requiredBy": ["/cssnano-preset-default/postcss-colormin"], "_resolved": "https://registry.npmjs.org/color/-/color-3.1.2.tgz", "_spec": "3.1.2", "_where": "F:\\xt_shipping", "authors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON>"], "bugs": {"url": "https://github.com/Qix-/color/issues"}, "dependencies": {"color-convert": "^1.9.1", "color-string": "^1.5.2"}, "description": "Color conversion and manipulation with CSS string support", "devDependencies": {"mocha": "^6.1.4", "xo": "0.12.1"}, "files": ["CHANGELOG.md", "LICENSE", "index.js"], "homepage": "https://github.com/Qix-/color#readme", "keywords": ["color", "colour", "css"], "license": "MIT", "name": "color", "repository": {"type": "git", "url": "git+https://github.com/Qix-/color.git"}, "scripts": {"pretest": "xo", "test": "mocha"}, "version": "3.1.2", "xo": {"rules": {"no-cond-assign": 0, "new-cap": 0}}}