{"title": "CSS Backdrop Filter", "description": "Method of applying filter effects (like blur, grayscale or hue) to content/elements below the target element.", "spec": "https://drafts.fxtf.org/filter-effects-2/#BackdropFilterProperty", "status": "unoff", "links": [{"url": "http://product.voxmedia.com/til/2015/2/17/8053347/css-ios-transparency-with-webkit-backdrop-filter", "title": "Blog post"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter", "title": "MDN Web Docs - CSS backdrop filter"}, {"url": "https://wpdev.uservoice.com/forums/257854-microsoft-edge-developer/suggestions/9160189-backdrop-filters", "title": "Edge feature request"}], "bugs": [{"description": "Chrome feature request: [Chromium issue #497522](https://code.google.com/p/chromium/issues/detail?id=497522)"}, {"description": "Firefox feature request: [Mozilla bug #1178765](https://bugzilla.mozilla.org/show_bug.cgi?id=1178765)"}], "categories": ["CSS", "CSS3"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "y #2", "18": "y #2", "76": "y"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "n", "49": "n", "50": "n", "51": "n", "52": "n", "53": "n", "54": "n", "55": "n", "56": "n", "57": "n", "58": "n", "59": "n", "60": "n", "61": "n", "62": "n", "63": "n", "64": "n", "65": "n", "66": "n", "67": "n", "68": "n", "69": "n", "70": "n"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n d #1", "48": "n d #1", "49": "n d #1", "50": "n d #1", "51": "n d #1", "52": "n d #1", "53": "n d #1", "54": "n d #1", "55": "n d #1", "56": "n d #1", "57": "n d #1", "58": "n d #1", "59": "n d #1", "60": "n d #1", "61": "n d #1", "62": "n d #1", "63": "n d #1", "64": "n d #1", "65": "n d #1", "66": "n d #1", "67": "n d #1", "68": "n d #1", "69": "n d #1", "70": "n d #1", "71": "n d #1", "72": "n d #1", "73": "n d #1", "74": "n d #1", "75": "n d #1", "76": "y", "77": "y", "78": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "y x", "9.1": "y x", "10": "y x", "10.1": "y x", "11": "y x", "11.1": "y x", "12": "y x", "12.1": "y x", "13": "y x", "TP": "y x"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n d #1", "35": "n d #1", "36": "n d #1", "37": "n d #1", "38": "n d #1", "39": "n d #1", "40": "n d #1", "41": "n d #1", "42": "n d #1", "43": "n d #1", "44": "n d #1", "45": "n d #1", "46": "n d #1", "47": "n d #1", "48": "n d #1", "49": "n d #1", "50": "n d #1", "51": "n d #1", "52": "n d #1", "53": "n d #1", "54": "n d #1", "55": "n d #1", "56": "n d #1", "57": "n d #1", "58": "n d #1", "60": "n d #1", "62": "n d #1"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "y x", "9.3": "y x", "10.0-10.2": "y x", "10.3": "y x", "11.0-11.2": "y x", "11.3-11.4": "y x", "12.0-12.1": "y x", "12.2-12.3": "y x", "13": "y x"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "67": "n"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "46": "n d #1"}, "and_chr": {"75": "n d #1"}, "and_ff": {"67": "n"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"12.12": "n"}, "samsung": {"4": "n", "5.0-5.4": "n d #1", "6.2-6.4": "n d #1", "7.2-7.4": "n d #1", "8.2": "n d #1", "9.2": "n d #1"}, "and_qq": {"1.2": "n d #1"}, "baidu": {"7.12": "n d #1"}, "kaios": {"2.5": "n"}}, "notes": "", "notes_by_num": {"1": "Can be enabled via the \"Experimental Web Platform Features\" flag", "2": "Currently only supported with the `-webkit-` prefix (not -ms-)"}, "usage_perc_y": 15.12, "usage_perc_a": 0, "ucprefix": false, "parent": "", "keywords": "blue,hue-rotate,invert,saturate,filter", "ie_id": "backdropfilter", "chrome_id": "5679432723333120", "firefox_id": "css-backdrop-filter", "webkit_id": "feature-filter-effects-backdrop-filter-property", "shown": true}