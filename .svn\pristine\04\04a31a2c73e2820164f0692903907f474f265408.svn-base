{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA,iCAAsE;AACtE,6CAAuC;AACvC,yDAAiD;AACjD,iCAMiB;AACjB,mCAA8B;AAmHrB,iBAnHF,gBAAM,CAmHE;AAjHf,SAAS,KAAK;IAAC,iBAA2B;SAA3B,UAA2B,EAA3B,qBAA2B,EAA3B,IAA2B;QAA3B,4BAA2B;;IACxC,gBAAgB;IAChB,kCAAkC;IAClC,kFAAkF;IAClF,yBAAyB;IACzB,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;QACxB,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;YAC7B,OAAO,kBAAS,yBAAC,EAAE,GAAK,OAAO,CAAC,CAAC,CAAC,GAAE,qBAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAE;SAC7D;QAED,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,eAAe,EAAE;YAC3D,OAAO;gBAAC,oBAAa;qBAAb,UAAa,EAAb,qBAAa,EAAb,IAAa;oBAAb,+BAAa;;gBACnB,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;oBAChC,OAAO,kBAAS,yBAAC,EAAE,GAAK,UAAU,CAAC,CAAC,CAAC,GAAE,qBAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAE;iBAChE;gBAED,OAAO,kBAAS,yBAAC,EAAE,GAAK,UAAU,GAAE,qBAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAE;YAC9D,CAAC,CAAC;SACH;QAED,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC;KACnB;IAED,OAAO,kBAAS,yBAAC,EAAE,GAAK,OAAO,GAAE,qBAAU,EAAE,IAAE;AACjD,CAAC;AAEY,QAAA,KAAK,GAAG,KAAK,CAAC;IACzB,cAAc,EAAE,UAAC,CAAM,EAAE,CAAM,EAAE,GAAQ;QACvC,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACvC,OAAO,kBAAS,CAAC,CAAC,EAAE,CAAC,EAAE,8BAAU,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;SACxD;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CACF,CAAC,CAAC;AAEU,QAAA,QAAQ,GAAG;IAAC,iBAA2B;SAA3B,UAA2B,EAA3B,qBAA2B,EAA3B,IAA2B;QAA3B,4BAA2B;;IAAK,OAAA,eAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AAAtB,CAAsB,CAAC;AAEhF,qDAAqD;AACrD,kDAAkD;AACrC,QAAA,QAAQ,GAAG,UAAC,KAA2B;IAA3B,sBAAA,EAAA,UAA2B;IAClD,OAAA,KAAK,CAAC;QACJ,cAAc,EAAE,cAAc,CAAC,KAAK,CAAC;QACrC,eAAe,EAAE,eAAe,CAAC,KAAK,CAAC;KACxC,CAAC;AAHF,CAGE,CAAC;AACL,IAAM,kBAAkB,GAAG,UAAC,KAAU;IAAV,sBAAA,EAAA,UAAU;IACpC,OAAA,KAAK,CAAC;QACJ,cAAc,EAAE,UAAC,CAAC,EAAE,CAAC,EAAE,GAAQ;YAC7B,IAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAE3C,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE;gBAClB,QAAQ,KAAK,CAAC,GAAG,CAAC,EAAE;oBAClB,KAAK,SAAS;wBACZ,gBACK,uBAAc,CAAC,CAAC,EAAE,CAAC,EAAE,UAAC,OAAO,EAAE,QAAQ;4BACxC,OAAA,8BAAU,CAAC,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC;wBAApD,CAAoD,CACrD,EACE,CAAC,EACJ;oBACJ,KAAK,SAAS;wBACZ,OAAO,CAAC,CAAC;oBACX;wBACE,SAAS;wBACT,OAAO,kBAAS,CAAC,CAAC,EAAE,CAAC,EAAE,8BAAU,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;iBAC7D;aACF;YAED,OAAO,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QAC1C,CAAC;QACD,eAAe,EAAE,eAAe,CAAC,KAAK,CAAC;KACxC,CAAC;AAxBF,CAwBE,CAAC;AAEL,SAAS,cAAc,CAAC,KAAsB;IAC5C,OAAO,UAAC,CAAM,EAAE,CAAM,EAAE,GAAkB;QACxC,QAAQ,KAAK,CAAC,GAAG,CAAC,EAAE;YAClB,KAAK,qBAAa,CAAC,OAAO;gBACxB,gBAAW,CAAC,EAAK,CAAC,EAAE;YACtB,KAAK,qBAAa,CAAC,OAAO;gBACxB,OAAO,CAAC,CAAC;YACX,KAAK,qBAAa,CAAC,MAAM,CAAC;YAC1B;gBACE,SAAS;gBACT,OAAO,KAAK,CAAC;SAChB;IACH,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,eAAe,CAAC,KAAsB;IAC7C,OAAO,UAAC,CAAM,EAAE,CAAM,EAAE,GAAkB;QACxC,QAAQ,KAAK,CAAC,GAAG,CAAC,EAAE;YAClB,KAAK,qBAAa,CAAC,OAAO;gBACxB,OAAO,kBAAS,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,qBAAU,EAAE,CAAC,CAAC;YAC3C,KAAK,qBAAa,CAAC,OAAO;gBACxB,OAAO,CAAC,CAAC;YACX,KAAK,qBAAa,CAAC,MAAM,CAAC;YAC1B;gBACE,OAAO,KAAK,CAAC;SAChB;IACH,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,MAAM,CAAC,GAAc;IAC5B,OAAO,CACL;QACE,iBAAS,CAAC,UAAU;QACpB,iBAAS,CAAC,OAAO;QACjB,iBAAS,CAAC,WAAW;QACrB,iBAAS,CAAC,KAAK;KAChB,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CACpB,CAAC;AACJ,CAAC;AAED,kBAAe,KAAK,CAAC"}