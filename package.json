{"name": "xtshipping", "version": "1.0.0", "description": "xtshipping official website", "author": "xtshipping", "private": true, "scripts": {"dev": "webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "pro": "webpack-dev-server --inline --progress --config build/webpack.pro.conf.js", "start": "npm run dev", "lint": "eslint --ext .js,.vue src", "build": "node build/build.js", "build:optimized": "node scripts/build-with-prerender.js", "build:prerender": "npm run build:optimized", "check:env": "node scripts/check-build-env.js"}, "dependencies": {"animate.css": "^3.7.2", "axios": "^0.19.0", "babel-polyfill": "^6.26.0", "bootstrap3": "^3.3.5", "element-dataset": "^2.2.6", "iview": "^3.4.2", "prerender-spa-plugin": "^3.4.0", "pupeteer": "0.0.1", "puppeteer": "^1.20.0", "puppeteer-chromium-resolver": "^5.2.0", "v-viewer": "^1.4.2", "video.js": "^8.0.4", "vue": "^2.5.2", "vue-awesome-swiper": "^3.1.3", "vue-baidu-map": "^0.21.22", "vue-router": "^3.1.3"}, "devDependencies": {"@vue/cli-plugin-eslint": "^3.0.1", "@vue/eslint-plugin": "^4.2.0", "autoprefixer": "^7.1.2", "babel-core": "^6.22.1", "babel-eslint": "^8.2.1", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-loader": "^7.1.1", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-runtime": "^6.22.0", "babel-plugin-transform-vue-jsx": "^3.5.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "chalk": "^2.0.1", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "eslint": "^4.19.1", "eslint-config-standard": "^10.2.1", "eslint-friendly-formatter": "^3.0.0", "eslint-loader": "^1.7.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-node": "^5.2.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^3.0.1", "extract-text-webpack-plugin": "^3.0.0", "file-loader": "^1.1.4", "friendly-errors-webpack-plugin": "^1.6.1", "html-webpack-plugin": "^2.30.1", "jquery": "^3.4.1", "less": "^3.9.0", "less-loader": "^5.0.0", "node-notifier": "^5.1.2", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^1.2.0", "portfinder": "^1.0.13", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.8", "postcss-url": "^7.2.1", "rimraf": "^2.6.0", "semver": "^5.3.0", "shelljs": "^0.7.6", "uglifyjs-webpack-plugin": "^1.1.1", "url-loader": "^0.5.8", "vue-loader": "^13.3.0", "vue-style-loader": "^3.0.1", "vue-template-compiler": "^2.5.2", "vuex": "^3.1.1", "webpack": "^3.6.0", "webpack-bundle-analyzer": "^2.9.0", "webpack-dev-server": "^2.9.1", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}