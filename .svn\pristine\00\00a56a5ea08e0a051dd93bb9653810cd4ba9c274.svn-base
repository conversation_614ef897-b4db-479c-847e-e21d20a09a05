{"_args": [["toidentifier@1.0.0", "F:\\xt_shipping"]], "_development": true, "_from": "toidentifier@1.0.0", "_id": "toidentifier@1.0.0", "_inBundle": false, "_integrity": "sha512-yaOH/Pk/VEhBWWTlhI+qXxDFXlejDGcQipMlyxda9nthulaxLZUNcUqFxokp0vcYnvteJln5FNQDRrxj3YcbVw==", "_location": "/toidentifier", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "toidentifier@1.0.0", "name": "toidentifier", "escapedName": "toidentifier", "rawSpec": "1.0.0", "saveSpec": null, "fetchSpec": "1.0.0"}, "_requiredBy": ["/http-errors"], "_resolved": "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.0.tgz", "_spec": "1.0.0", "_where": "F:\\xt_shipping", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/component/toidentifier/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://niftylettuce.com/"}], "description": "Convert a string of words to a JavaScript identifier", "devDependencies": {"eslint": "4.19.1", "eslint-config-standard": "11.0.0", "eslint-plugin-import": "2.11.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-node": "6.0.1", "eslint-plugin-promise": "3.7.0", "eslint-plugin-standard": "3.1.0", "mocha": "1.21.5", "nyc": "11.8.0"}, "engines": {"node": ">=0.6"}, "files": ["index.js"], "homepage": "https://github.com/component/toidentifier#readme", "license": "MIT", "name": "toidentifier", "repository": {"type": "git", "url": "git+https://github.com/component/toidentifier.git"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "version": "1.0.0"}