{"version": 3, "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///webpack/bootstrap 0eb96c1094e30c888e41", "webpack:///./src/locale/lang.js", "webpack:///external {\"root\":\"Vue\",\"commonjs\":\"vue\",\"commonjs2\":\"vue\",\"amd\":\"vue\"}", "webpack:///./src/locale/lang/ar-EG.js"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "self", "this", "__WEBPACK_EXTERNAL_MODULE_1__", "installedModules", "__webpack_require__", "moduleId", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "configurable", "enumerable", "get", "n", "__esModule", "object", "property", "prototype", "hasOwnProperty", "p", "s", "lang", "isServer", "window", "iview", "langs", "locale", "<PERSON><PERSON>", "$isServer", "select", "placeholder", "noMatch", "loading", "table", "noDataText", "noFilteredDataText", "confirmFilter", "resetFilter", "clearFilter", "datepicker", "selectDate", "selectTime", "startTime", "endTime", "clear", "ok", "datePanelLabel", "month", "month1", "month2", "month3", "month4", "month5", "month6", "month7", "month8", "month9", "month10", "month11", "month12", "year", "weekStartDay", "weeks", "sun", "mon", "tue", "wed", "thu", "fri", "sat", "months", "m1", "m2", "m3", "m4", "m5", "m6", "m7", "m8", "m9", "m10", "m11", "m12", "transfer", "titles", "source", "target", "filterPlaceholder", "notFoundText", "modal", "okText", "cancelText", "poptip", "page", "prev", "next", "total", "item", "items", "prev5", "next5", "goto", "rate", "star", "stars", "time", "before", "after", "just", "seconds", "minutes", "hours", "days", "tree", "emptyText", "default"], "mappings": "CAAA,SAAAA,EAAAC,GACA,iBAAAC,SAAA,iBAAAC,OACAA,OAAAD,QAAAD,EAAAG,QAAA,QACA,mBAAAC,eAAAC,IACAD,OAAA,uBAAAJ,GACA,iBAAAC,QACAA,QAAA,gBAAAD,EAAAG,QAAA,QAEAJ,EAAA,gBAAAC,EAAAD,EAAA,KARA,CASC,oBAAAO,UAAAC,KAAA,SAAAC,GACD,mBCTA,IAAAC,KAGA,SAAAC,EAAAC,GAGA,GAAAF,EAAAE,GACA,OAAAF,EAAAE,GAAAV,QAGA,IAAAC,EAAAO,EAAAE,IACAC,EAAAD,EACAE,GAAA,EACAZ,YAUA,OANAa,EAAAH,GAAAI,KAAAb,EAAAD,QAAAC,IAAAD,QAAAS,GAGAR,EAAAW,GAAA,EAGAX,EAAAD,QAqCA,OAhCAS,EAAAM,EAAAF,EAGAJ,EAAAO,EAAAR,EAGAC,EAAAQ,EAAA,SAAAjB,EAAAkB,EAAAC,GACAV,EAAAW,EAAApB,EAAAkB,IACAG,OAAAC,eAAAtB,EAAAkB,GACAK,cAAA,EACAC,YAAA,EACAC,IAAAN,KAMAV,EAAAiB,EAAA,SAAAzB,GACA,IAAAkB,EAAAlB,KAAA0B,WACA,WAA2B,OAAA1B,EAAA,SAC3B,WAAiC,OAAAA,GAEjC,OADAQ,EAAAQ,EAAAE,EAAA,IAAAA,GACAA,GAIAV,EAAAW,EAAA,SAAAQ,EAAAC,GAAsD,OAAAR,OAAAS,UAAAC,eAAAjB,KAAAc,EAAAC,IAGtDpB,EAAAuB,EAAA,gBAGAvB,IAAAwB,EAAA,6FCxDe,SAAUC,GAChBC,QAC2B,IAAjBC,OAAOC,QACR,UAAWA,QACbA,MAAMC,UAEVD,MAAMC,MAAMJ,EAAKvB,EAAE4B,QAAUL,IARzC,IAAMC,oDADN1B,EAAA,IACiB+B,QAAIV,UAAUW,yBCH/BxC,EAAAD,QAAAO,iFCEA,IAAM2B,GACFvB,GACI4B,OAAQ,QACRG,QACIC,YAAa,SACbC,QAAS,4BACTC,QAAS,SAEbC,OACIC,WAAY,iBACZC,mBAAoB,iBACpBC,cAAe,QACfC,YAAa,cACbC,YAAa,QAEjBC,YACIC,WAAY,eACZC,WAAY,aACZC,UAAW,cACXC,QAAS,cACTC,MAAO,QACPC,GAAI,OACJC,eAAgB,gBAChBC,MAAO,MACPC,OAAQ,QACRC,OAAQ,SACRC,OAAQ,OACRC,OAAQ,QACRC,OAAQ,OACRC,OAAQ,QACRC,OAAQ,QACRC,OAAQ,QACRC,OAAQ,SACRC,QAAS,SACTC,QAAS,SACTC,QAAS,SACTC,KAAM,MACNC,aAAc,IACdC,OACIC,IAAK,MACLC,IAAK,QACLC,IAAK,SACLC,IAAK,SACLC,IAAK,OACLC,IAAK,OACLC,IAAK,OAETC,QACIC,GAAI,QACJC,GAAI,SACJC,GAAI,OACJC,GAAI,QACJC,GAAI,OACJC,GAAI,QACJC,GAAI,QACJC,GAAI,QACJC,GAAI,SACJC,IAAK,SACLC,IAAK,SACLC,IAAK,WAGbC,UACIC,QACIC,OAAQ,SACRC,OAAQ,SAEZC,kBAAmB,WACnBC,aAAc,WAElBC,OACIC,OAAQ,OACRC,WAAY,SAEhBC,QACIF,OAAQ,OACRC,WAAY,SAEhBE,MACIC,KAAM,iBACNC,KAAM,iBACNC,MAAO,UACPC,KAAM,OACNC,MAAO,QACPC,MAAO,sBACPC,MAAO,sBACPP,KAAM,QACNQ,KAAM,aACNlF,EAAG,IAEPmF,MACIC,KAAM,OACNC,MAAO,QAEXC,MACIC,OAAQ,OACRC,MAAO,OACPC,KAAM,OACNC,QAAS,SACTC,QAAS,SACTC,MAAO,SACPC,KAAM,SAEVC,MACIC,UAAW,qBAKvB,oDA/GAtH,EAAA,IA+GAuH,SAAQ9F,aAEOA", "file": "ar-EG.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"vue\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"iview/locale\", [\"vue\"], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"iview/locale\"] = factory(require(\"vue\"));\n\telse\n\t\troot[\"iview/locale\"] = factory(root[\"Vue\"]);\n})(typeof self !== 'undefined' ? self : this, function(__WEBPACK_EXTERNAL_MODULE_1__) {\nreturn \n\n\n// WEBPACK FOOTER //\n// webpack/universalModuleDefinition", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, {\n \t\t\t\tconfigurable: false,\n \t\t\t\tenumerable: true,\n \t\t\t\tget: getter\n \t\t\t});\n \t\t}\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"/dist/locale/\";\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 2);\n\n\n\n// WEBPACK FOOTER //\n// webpack/bootstrap 0eb96c1094e30c888e41", "// using with vue-i18n in CDN\n/*eslint-disable */\nimport Vue from 'vue';\nconst isServer = Vue.prototype.$isServer;\n\nexport default function (lang) {\n    if (!isServer) {\n        if (typeof window.iview !== 'undefined') {\n            if (!('langs' in iview)) {\n                iview.langs = {};\n            }\n            iview.langs[lang.i.locale] = lang;\n        }\n    }\n};\n/*eslint-enable */\n\n\n// WEBPACK FOOTER //\n// ./src/locale/lang.js", "module.exports = __WEBPACK_EXTERNAL_MODULE_1__;\n\n\n//////////////////\n// WEBPACK FOOTER\n// external {\"root\":\"Vue\",\"commonjs\":\"vue\",\"commonjs2\":\"vue\",\"amd\":\"vue\"}\n// module id = 1\n// module chunks = 0 1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16 17 18 19 20 21 22 23 24 25 26 27 28 29 30", "import setLang from '../lang';\n\nconst lang = {\n    i: {\n        locale: 'ar-EG',\n        select: {\n            placeholder: 'إختيار',\n            noMatch: 'لا يوجد تطابق في البيانات',\n            loading: 'تحميل'\n        },\n        table: {\n            noDataText: 'لا توجد بيانات',\n            noFilteredDataText: 'لا توجد بيانات',\n            confirmFilter: 'تأكيد',\n            resetFilter: 'إعادة تعيين',\n            clearFilter: 'الكل'\n        },\n        datepicker: {\n            selectDate: 'إختر التاريخ',\n            selectTime: 'إختر الوقت',\n            startTime: 'وقت البداية',\n            endTime: 'وقت النهاية',\n            clear: 'إزالة',\n            ok: 'حسنا',\n            datePanelLabel: '[mmmm] [yyyy]',\n            month: 'شهر',\n            month1: 'يناير',\n            month2: 'فبراير',\n            month3: 'مارس',\n            month4: 'أبريل',\n            month5: 'مايو',\n            month6: 'يونيو',\n            month7: 'يوليو',\n            month8: 'أغسطس',\n            month9: 'سبتمبر',\n            month10: 'اكتوبر',\n            month11: 'نوفمبر',\n            month12: 'ديسمبر',\n            year: 'سنة',\n            weekStartDay: '0',\n            weeks: {\n                sun: 'أحد',\n                mon: 'إثنين',\n                tue: 'ثلاثاء',\n                wed: 'أربعاء',\n                thu: 'خميس',\n                fri: 'جمعة',\n                sat: 'سبت'\n            },\n            months: {\n                m1: 'يناير',\n                m2: 'فبراير',\n                m3: 'مارس',\n                m4: 'أبريل',\n                m5: 'مايو',\n                m6: 'يونيو',\n                m7: 'يوليو',\n                m8: 'أغسطس',\n                m9: 'سبتمبر',\n                m10: 'اكتوبر',\n                m11: 'نوفمبر',\n                m12: 'ديسمبر'\n            }\n        },\n        transfer: {\n            titles: {\n                source: 'المصدر',\n                target: 'الهدف'\n            },\n            filterPlaceholder: 'إبحث هنا',\n            notFoundText: 'لا يوجد'\n        },\n        modal: {\n            okText: 'حسنا',\n            cancelText: 'إلغاء'\n        },\n        poptip: {\n            okText: 'حسنا',\n            cancelText: 'إلغاء'\n        },\n        page: {\n            prev: 'الصفحة السابقة',\n            next: 'الصفحة التالية',\n            total: 'المجموع',\n            item: 'عنصر',\n            items: 'عناصر',\n            prev5: 'الخمس صفحات السابقة',\n            next5: 'الخمس صفحات التالية',\n            page: '/صفحة',\n            goto: 'الذهاب إلى',\n            p: ''\n        },\n        rate: {\n            star: 'نجمة',\n            stars: 'نجوم'\n        },\n        time: {\n            before: ' منذ',\n            after: ' بعد',\n            just: 'الآن',\n            seconds: ' ثواني',\n            minutes: ' دقائق',\n            hours: ' ساعات',\n            days: ' أيام'\n        },\n        tree: {\n            emptyText: 'لا توجد بيانات'\n        }\n    }\n};\n\nsetLang(lang);\n\nexport default lang;\n\n\n\n// WEBPACK FOOTER //\n// ./src/locale/lang/ar-EG.js"], "sourceRoot": ""}