{"_args": [["copy-webpack-plugin@4.6.0", "F:\\xt_shipping"]], "_development": true, "_from": "copy-webpack-plugin@4.6.0", "_id": "copy-webpack-plugin@4.6.0", "_inBundle": false, "_integrity": "sha512-Y+SQCF+0NoWQryez2zXn5J5knmr9z/9qSQt7fbL78u83rxmigOy8X5+BFn8CFSuX+nKT8gpYwJX68ekqtQt6ZA==", "_location": "/copy-webpack-plugin", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "copy-webpack-plugin@4.6.0", "name": "copy-webpack-plugin", "escapedName": "copy-webpack-plugin", "rawSpec": "4.6.0", "saveSpec": null, "fetchSpec": "4.6.0"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npmjs.org/copy-webpack-plugin/-/copy-webpack-plugin-4.6.0.tgz", "_spec": "4.6.0", "_where": "F:\\xt_shipping", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/webpack-contrib/copy-webpack-plugin/issues"}, "dependencies": {"cacache": "^10.0.4", "find-cache-dir": "^1.0.0", "globby": "^7.1.1", "is-glob": "^4.0.0", "loader-utils": "^1.1.0", "minimatch": "^3.0.4", "p-limit": "^1.0.0", "serialize-javascript": "^1.4.0"}, "description": "Copy files && directories with webpack", "devDependencies": {"babel-cli": "^6.8.0", "babel-preset-es2015": "^6.6.0", "chai": "^3.4.0", "enhanced-resolve": "^3.4.1", "eslint": "^2.9.0", "is-gzip": "^2.0.0", "mkdirp": "^0.5.1", "mocha": "^2.4.5", "ncp": "^2.0.0", "rimraf": "^2.6.2", "standard-version": "^4.2.0"}, "engines": {"node": ">= 4"}, "files": ["dist"], "homepage": "https://github.com/webpack-contrib/copy-webpack-plugin", "keywords": ["webpack", "plugin", "transfer", "move", "copy"], "license": "MIT", "main": "dist/index.js", "name": "copy-webpack-plugin", "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/copy-webpack-plugin.git"}, "scripts": {"build": "babel src/ --out-dir dist/", "build:tests": "babel tests/ --out-dir compiled_tests/ && rimraf compiled_tests/helpers && ncp tests/helpers compiled_tests/helpers && node scripts/createSpecialDirectory.js", "lint": "eslint src/ tests/", "prepare": "npm run build", "pretest": "npm run lint && npm run build && npm run build:tests", "release": "standard-version", "test": "mocha compiled_tests/"}, "version": "4.6.0"}