{"_args": [["slice-ansi@1.0.0", "F:\\xt_shipping"]], "_development": true, "_from": "slice-ansi@1.0.0", "_id": "slice-ansi@1.0.0", "_inBundle": false, "_integrity": "sha512-POqxBK6Lb3q6s047D/XsDVNPnF9Dl8JSaqe9h9lURl0OdNqy/ujDrOiIHtsqXMGbWWTIomRzAMaTyawAU//Reg==", "_location": "/slice-ansi", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "slice-ansi@1.0.0", "name": "slice-ansi", "escapedName": "slice-ansi", "rawSpec": "1.0.0", "saveSpec": null, "fetchSpec": "1.0.0"}, "_requiredBy": ["/table"], "_resolved": "https://registry.npmjs.org/slice-ansi/-/slice-ansi-1.0.0.tgz", "_spec": "1.0.0", "_where": "F:\\xt_shipping", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/chalk/slice-ansi/issues"}, "dependencies": {"is-fullwidth-code-point": "^2.0.0"}, "description": "Slice a string with ANSI escape codes", "devDependencies": {"ava": "*", "chalk": "^2.0.1", "random-item": "^1.0.0", "strip-ansi": "^4.0.0", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/chalk/slice-ansi#readme", "keywords": ["slice", "string", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "name": "slice-ansi", "repository": {"type": "git", "url": "git+https://github.com/chalk/slice-ansi.git"}, "scripts": {"test": "xo && ava"}, "version": "1.0.0"}