#hidden {
  color: transparent;
}
#hidden1 {
  color: transparent;
}
.two-args {
  color: blue;
  width: 10px;
  height: 99%;
  border: 2px dotted black;
}
.one-arg {
  width: 15px;
  height: 49%;
}
.no-parens {
  width: 5px;
  height: 49%;
}
.no-args {
  width: 5px;
  height: 49%;
}
.var-args {
  width: 45;
  height: 17%;
}
.multi-mix {
  width: 10px;
  height: 29%;
  margin: 4;
  padding: 5;
}
body {
  padding: 30px;
  color: #f00;
}
.scope-mix {
  width: 8;
}
.content {
  width: 600px;
}
.content .column {
  margin: 600px;
}
#same-var-name {
  radius: 5px;
}
#var-inside {
  width: 10px;
}
.arguments {
  border: 1px solid black;
  width: 1px;
}
.arguments2 {
  border: 0px;
  width: 0px;
}
.arguments3 {
  border: 0px;
  width: 0px;
}
.arguments4 {
  border: 0 1 2 3 4;
  rest: 1 2 3 4;
  width: 0;
}
.edge-case {
  border: "{";
  width: "{";
}
.slash-vs-math {
  border-radius: 2px/5px;
  border-radius: 5px/10px;
  border-radius: 6px;
}
.comma-vs-semi-colon {
  one: a;
  two: b, c;
  one: d, e;
  two: f;
  one: g;
  one: h;
  one: i;
  one: j;
  one: k;
  two: l;
  one: m, n;
  one: o, p;
  two: q;
  one: r, s;
  two: t;
}
#named-conflict {
  four: a, 11, 12, 13;
  four: a, 21, 22, 23;
}
.test-mixin-default-arg {
  defaults: 1px 1px 1px;
  defaults: 2px 2px 2px;
}
.selector {
  margin: 2, 2, 2, 2;
}
.selector2 {
  margin: 2, 2, 2, 2;
}
.selector3 {
  margin: 4;
}
mixins-args-expand-op-1 {
  m3: 1, 2, 3;
}
mixins-args-expand-op-2 {
  m3: 4, 5, 6;
}
mixins-args-expand-op-3a {
  m3: a, b, c;
}
mixins-args-expand-op-3b {
  m4: 0, a, b, c;
}
mixins-args-expand-op-3c {
  m4: a, b, c, 4;
}
mixins-args-expand-op-4a {
  m3: a, b, c, d;
}
mixins-args-expand-op-4b {
  m4: 0, a, b, c, d;
}
mixins-args-expand-op-4c {
  m4: a, b, c, d, 4;
}
mixins-args-expand-op-5a {
  m3: 1, 2, 3;
}
mixins-args-expand-op-5b {
  m4: 0, 1, 2, 3;
}
mixins-args-expand-op-5c {
  m4: 1, 2, 3, 4;
}
mixins-args-expand-op-6 {
  m4: 0, 1, 2, 3;
}
mixins-args-expand-op-7 {
  m4: 0, 1, 2, 3;
}
mixins-args-expand-op-8 {
  m4: 1, 1.5, 2, 3;
}
mixins-args-expand-op-9 {
  aa: 4 5 6 1 2 3 and again 4 5 6;
  a4: and;
  a8: 5;
}
#test-mixin-matching-when-default-2645 {
  height: 20px;
}
