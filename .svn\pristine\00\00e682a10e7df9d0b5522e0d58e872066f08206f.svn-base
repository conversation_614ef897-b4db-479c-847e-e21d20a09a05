{"_args": [["is-accessor-descriptor@1.0.0", "F:\\xt_shipping"]], "_development": true, "_from": "is-accessor-descriptor@1.0.0", "_id": "is-accessor-descriptor@1.0.0", "_inBundle": false, "_integrity": "sha512-m5hnHTkcVsPfqx3AKlyttIPb7J+XykHvJP2B9bZDjlhLIoEq4XoK64Vg7boZlVWYK6LUY94dYPEE7Lh0ZkZKcQ==", "_location": "/define-property/is-accessor-descriptor", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "is-accessor-descriptor@1.0.0", "name": "is-accessor-descriptor", "escapedName": "is-accessor-descriptor", "rawSpec": "1.0.0", "saveSpec": null, "fetchSpec": "1.0.0"}, "_requiredBy": ["/define-property/is-descriptor"], "_resolved": "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "_spec": "1.0.0", "_where": "F:\\xt_shipping", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/is-accessor-descriptor/issues"}, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON><PERSON>", "url": "www.rouvenwessling.de"}], "dependencies": {"kind-of": "^6.0.0"}, "description": "Returns true if a value has the characteristics of a valid JavaScript accessor descriptor.", "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.5.3"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/is-accessor-descriptor", "keywords": ["accessor", "check", "data", "descriptor", "get", "getter", "is", "keys", "object", "properties", "property", "set", "setter", "type", "valid", "value"], "license": "MIT", "main": "index.js", "name": "is-accessor-descriptor", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/is-accessor-descriptor.git"}, "scripts": {"test": "mocha"}, "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["is-accessor-descriptor", "is-data-descriptor", "is-descriptor", "is-plain-object", "isobject"]}, "lint": {"reflinks": true}}, "version": "1.0.0"}