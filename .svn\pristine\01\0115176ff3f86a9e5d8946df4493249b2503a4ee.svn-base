<template>
  <div v-html="desc"></div>
</template>
<script>
import { getCurList } from '@/api/api'

export default {
  data () {
    return {
      desc: '暂无相关介绍',
      listQuery: {
        pageSize: 10,
        pageNumber: 1,
        category_code: 1000901
      }
    }
  },
  methods: {
    getList () {
      getCurList(this.listQuery).then(res => {
        if (res.data.Code === 10000) {
          this.desc = res.data.rows[0].article_content
        } else {
          this.desc = '暂无相关介绍'
          this.$Message.error(res.data.Message)
        }
      })
    }
  },
  created () {
    this.getList()
  }
}
</script>
