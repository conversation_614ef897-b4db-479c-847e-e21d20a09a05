{"_args": [["parallel-transform@1.1.0", "F:\\xt_shipping"]], "_development": true, "_from": "parallel-transform@1.1.0", "_id": "parallel-transform@1.1.0", "_inBundle": false, "_integrity": "sha1-1BDwZbBdojCB/NEPKIVMKb2jOwY=", "_location": "/parallel-transform", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "parallel-transform@1.1.0", "name": "parallel-transform", "escapedName": "parallel-transform", "rawSpec": "1.1.0", "saveSpec": null, "fetchSpec": "1.1.0"}, "_requiredBy": ["/mississippi", "/terser-webpack-plugin/mississippi"], "_resolved": "https://registry.npmjs.org/parallel-transform/-/parallel-transform-1.1.0.tgz", "_spec": "1.1.0", "_where": "F:\\xt_shipping", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/mafintosh/parallel-transform/issues"}, "dependencies": {"cyclist": "~0.2.2", "inherits": "^2.0.3", "readable-stream": "^2.1.5"}, "description": "Transform stream that allows you to run your transforms in parallel without changing the order", "homepage": "https://github.com/mafintosh/parallel-transform#readme", "keywords": ["transform", "stream", "parallel", "preserve", "order"], "license": "MIT", "name": "parallel-transform", "repository": {"type": "git", "url": "git://github.com/mafintosh/parallel-transform.git"}, "version": "1.1.0"}