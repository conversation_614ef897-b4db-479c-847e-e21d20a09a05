<template>
  <div class="leftSider col-md-3 col-sm-4">
    <Menu
      :mode="this.$store.state.browserSize.width < 768 ? 'horizontal': 'vertical'"
      :theme="curTheme"
      :active-name="activeId"
      @on-select="menuSelect"
      style="width:auto;"
    >
      <div class="sider-title animated" data-ani="fadeInDown" data-delay="300">
        <p class="sider-cn">{{ isEn ? nav[0].entitle : nav[0].title }}</p>
        <p class="sider-en" v-if="!isEn">{{ nav[0].entitle }}</p>
      </div>
      <div v-for="(list, index) in nav" :key="index" class="animated inline-block" data-ani="fadeInUp">
        <MenuItem :name="(index + 1)" :to="list.path === '' ? list.url : list.path" :target="list.path === '' ? '_blank' : '_self'" v-if="!list.children">
          <!-- <Icon :type="list.icon"/> -->
          {{ isEn ? list.enName : list.name }}
        </MenuItem>
        <Submenu :name="(index + 1)" v-else>
          <template slot="title">
              <!-- <Icon type="ios-paper"></Icon> -->
              {{ isEn ? list.enName : list.name }}
          </template>
          <MenuItem v-show="(!isEn || item.name !== '十大信条')" :name="(index + 1) + '-' + (idx + 1)" v-for="(item, idx) in list.children" :key="idx" :to="item.path">
            {{ isEn ? item.enName : item.name }}
          </MenuItem>
        </Submenu>
      </div>
    </Menu>
  </div>
</template>
<script>
export default {
  props: {
    nav: Array
  },
  data() {
    return {
      curTheme: "light"
    };
  },
  created () {
    
  },
  computed: {
      // activeId: this.$route.query.cid ?  parseInt(this.$route.query.id) + '-' + parseInt(this.$route.query.cid) : parseInt(this.$route.query.id) || 1,
    activeId: {
      get() {
        return this.$route.query.cid ?  parseInt(this.$route.query.id) + '-' + parseInt(this.$route.query.cid) : parseInt(this.$route.query.id) || 1
      },
      set(newVal) {
        return newVal
      }
      // console.log(this.$route.query.cid ?  parseInt(this.$route.query.id) + '-' + parseInt(this.$route.query.cid) : parseInt(this.$route.query.id) || 1)
      // return this.$route.query.cid ?  parseInt(this.$route.query.id) + '-' + parseInt(this.$route.query.cid) : parseInt(this.$route.query.id) || 1
    },
    isEn () {
      return this.$store.state.isEn
    }
  },
  methods: {
    menuSelect(id) {
      let _curIndex = parseInt(id) - 1;
      let _obj = {
        name: this.nav[_curIndex].name,
        id: id
      };
      // this.$emit("nav-back", _obj);
    }
  },
  watch: {
    $route(newRoute) {
      this.activeId = parseInt(newRoute.query.id) || 1;
    }
  }
};
</script>
<style scoped>
.breadcrumb-separator {
  color: #999;
}
.leftSider .ivu-menu-vertical {
  padding-top: 30px;
}
.leftSider {
  padding-left: 0;
}
.breadcrumb-separator {
  color: #999;
}
.col-md-3,
.col-sm-3 {
  position: static;
}
.sider-cn {
  font-size: 24px;
}
.sider-en {
  margin-top: 10px;
  font-size: 12px;
  line-height: 12px;
}
@media only screen and (max-width: 767px) {
  .sider-cn {
    font-size: 22px;
  }
  .inline-block {
    display: inline-block;
  }
}
</style>