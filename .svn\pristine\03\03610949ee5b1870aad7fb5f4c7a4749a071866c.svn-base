{"_args": [["js-queue@2.0.0", "F:\\xt_shipping"]], "_development": true, "_from": "js-queue@2.0.0", "_id": "js-queue@2.0.0", "_inBundle": false, "_integrity": "sha1-NiITz4YPRo8BJfxslqvBdCUx+Ug=", "_location": "/js-queue", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "js-queue@2.0.0", "name": "js-queue", "escapedName": "js-queue", "rawSpec": "2.0.0", "saveSpec": null, "fetchSpec": "2.0.0"}, "_requiredBy": ["/node-ipc"], "_resolved": "https://registry.npmjs.org/js-queue/-/js-queue-2.0.0.tgz", "_spec": "2.0.0", "_where": "F:\\xt_shipping", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/RIAEvangelist/js-queue/issues"}, "dependencies": {"easy-stack": "^1.0.0"}, "description": "Simple JS queue with auto run for node and browsers", "engines": {"node": ">=1.0.0"}, "homepage": "https://github.com/RIAEvangelist/js-queue#readme", "keywords": ["queue", "node", "js", "auto", "run", "execute", "browser", "react"], "license": "DBAD", "main": "queue.js", "name": "js-queue", "repository": {"type": "git", "url": "git+https://github.com/RIAEvangelist/js-queue.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "version": "2.0.0"}