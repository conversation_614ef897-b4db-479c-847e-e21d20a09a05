{"_args": [["https-browserify@1.0.0", "F:\\xt_shipping"]], "_development": true, "_from": "https-browserify@1.0.0", "_id": "https-browserify@1.0.0", "_inBundle": false, "_integrity": "sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM=", "_location": "/https-browserify", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "https-browserify@1.0.0", "name": "https-browserify", "escapedName": "https-browserify", "rawSpec": "1.0.0", "saveSpec": null, "fetchSpec": "1.0.0"}, "_requiredBy": ["/node-libs-browser"], "_resolved": "https://registry.npmjs.org/https-browserify/-/https-browserify-1.0.0.tgz", "_spec": "1.0.0", "_where": "F:\\xt_shipping", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bugs": {"url": "https://github.com/substack/https-browserify/issues"}, "description": "https module compatability for browserify", "devDependencies": {"standard": "^9.0.2"}, "homepage": "https://github.com/substack/https-browserify", "keywords": ["browser", "browserify", "https"], "license": "MIT", "main": "index.js", "name": "https-browserify", "repository": {"type": "git", "url": "git://github.com/substack/https-browserify.git"}, "scripts": {"test": "standard"}, "version": "1.0.0"}