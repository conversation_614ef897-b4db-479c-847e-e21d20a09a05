{"_args": [["is-ci@1.2.1", "F:\\xt_shipping"]], "_development": true, "_from": "is-ci@1.2.1", "_id": "is-ci@1.2.1", "_inBundle": false, "_integrity": "sha512-s6tfsaQaQi3JNciBH6shVqEDvhGut0SUXr31ag8Pd8BBbVVlcGfWhpPmEOoM6RJ5TFhbypvf5yyRw/VXW1IiWg==", "_location": "/is-ci", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "is-ci@1.2.1", "name": "is-ci", "escapedName": "is-ci", "rawSpec": "1.2.1", "saveSpec": null, "fetchSpec": "1.2.1"}, "_requiredBy": ["/yorkie"], "_resolved": "https://registry.npmjs.org/is-ci/-/is-ci-1.2.1.tgz", "_spec": "1.2.1", "_where": "F:\\xt_shipping", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/wa7son"}, "bin": {"is-ci": "bin.js"}, "bugs": {"url": "https://github.com/watson/is-ci/issues"}, "coordinates": [55.778255, 12.593033], "dependencies": {"ci-info": "^1.5.0"}, "description": "Detect if the current environment is a CI server", "devDependencies": {"clear-require": "^1.0.1", "standard": "^11.0.1"}, "homepage": "https://github.com/watson/is-ci", "keywords": ["ci", "continuous", "integration", "test", "detect"], "license": "MIT", "main": "index.js", "name": "is-ci", "repository": {"type": "git", "url": "git+https://github.com/watson/is-ci.git"}, "scripts": {"test": "standard && node test.js"}, "version": "1.2.1"}