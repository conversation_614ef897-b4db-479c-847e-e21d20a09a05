{"_args": [["@vue/cli-shared-utils@3.9.0", "F:\\xt_shipping"]], "_development": true, "_from": "@vue/cli-shared-utils@3.9.0", "_id": "@vue/cli-shared-utils@3.9.0", "_inBundle": false, "_integrity": "sha512-wumeMZTz5aQ+1Y6uxTKegIsgOXEWT3hT8f9sW2mj5SwNDVyQ+AHZTgSynYExTUJg3dH81uKgFDUpPdAvGxzh8g==", "_location": "/@vue/cli-shared-utils", "_phantomChildren": {"chalk": "2.4.2", "cli-cursor": "2.1.0", "end-of-stream": "1.4.1", "is-stream": "1.1.0", "log-symbols": "2.2.0", "nice-try": "1.0.5", "npm-run-path": "2.0.2", "once": "1.4.0", "p-finally": "1.0.0", "path-key": "2.0.1", "shebang-command": "1.2.0", "signal-exit": "3.0.2", "strip-eof": "1.0.0", "wcwidth": "1.0.1", "which": "1.3.1"}, "_requested": {"type": "version", "registry": true, "raw": "@vue/cli-shared-utils@3.9.0", "name": "@vue/cli-shared-utils", "escapedName": "@vue%2fcli-shared-utils", "scope": "@vue", "rawSpec": "3.9.0", "saveSpec": null, "fetchSpec": "3.9.0"}, "_requiredBy": ["/@vue/cli-plugin-eslint"], "_resolved": "https://registry.npmjs.org/@vue/cli-shared-utils/-/cli-shared-utils-3.9.0.tgz", "_spec": "3.9.0", "_where": "F:\\xt_shipping", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "dependencies": {"@hapi/joi": "^15.0.1", "chalk": "^2.4.1", "execa": "^1.0.0", "launch-editor": "^2.2.1", "lru-cache": "^5.1.1", "node-ipc": "^9.1.1", "open": "^6.3.0", "ora": "^3.4.0", "request": "^2.87.0", "request-promise-native": "^1.0.7", "semver": "^6.0.0", "string.prototype.padstart": "^3.0.0"}, "description": "shared utilities for vue-cli packages", "gitHead": "f276598ece07204e5b8b54e4bbcccfa81cd1b478", "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-shared-utils#readme", "keywords": ["vue", "cli", "cli-shared-utils"], "license": "MIT", "main": "index.js", "name": "@vue/cli-shared-utils", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-shared-utils"}, "version": "3.9.0"}