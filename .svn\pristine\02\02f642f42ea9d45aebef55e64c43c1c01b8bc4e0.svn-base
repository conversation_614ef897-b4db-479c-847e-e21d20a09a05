{"_args": [["fast-le<PERSON><PERSON><PERSON>@2.0.6", "F:\\xt_shipping"]], "_development": true, "_from": "fast-le<PERSON><PERSON><PERSON>@2.0.6", "_id": "fast-le<PERSON><PERSON><PERSON>@2.0.6", "_inBundle": false, "_integrity": "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=", "_location": "/fast-le<PERSON><PERSON><PERSON>", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "fast-le<PERSON><PERSON><PERSON>@2.0.6", "name": "fast-le<PERSON><PERSON><PERSON>", "escapedName": "fast-le<PERSON><PERSON><PERSON>", "rawSpec": "2.0.6", "saveSpec": null, "fetchSpec": "2.0.6"}, "_requiredBy": ["/optionator"], "_resolved": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "_spec": "2.0.6", "_where": "F:\\xt_shipping", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.hiddentao.com/"}, "bugs": {"url": "https://github.com/hiddentao/fast-levenshtein/issues"}, "description": "Efficient implementation of Levenshtein algorithm  with locale-specific collator support.", "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "grunt-benchmark": "~0.2.0", "grunt-cli": "^1.2.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0", "grunt-mocha-test": "~0.2.2", "grunt-npm-install": "~0.1.0", "load-grunt-tasks": "~0.6.0", "lodash": "^4.0.1", "mocha": "~1.9.0"}, "files": ["levenshtein.js"], "homepage": "https://github.com/hiddentao/fast-levenshtein#readme", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "string"], "license": "MIT", "main": "levenshtein.js", "name": "fast-le<PERSON><PERSON><PERSON>", "repository": {"type": "git", "url": "git+https://github.com/hiddentao/fast-levenshtein.git"}, "scripts": {"benchmark": "grunt benchmark", "build": "grunt build", "prepublish": "npm run build", "test": "mocha"}, "version": "2.0.6"}