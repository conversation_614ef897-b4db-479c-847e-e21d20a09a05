{"_from": "less-loader", "_id": "less-loader@5.0.0", "_inBundle": false, "_integrity": "sha512-bquCU89mO/yWLaUq0Clk7qCsKhsF/TZpJUzETRvJa9KSVEL9SO3ovCvdEHISBhrC81OwC8QSVX7E0bzElZj9cg==", "_location": "/less-loader", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "less-loader", "name": "less-loader", "escapedName": "less-loader", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#DEV:/", "#USER"], "_resolved": "https://registry.npmjs.org/less-loader/-/less-loader-5.0.0.tgz", "_shasum": "498dde3a6c6c4f887458ee9ed3f086a12ad1b466", "_spec": "less-loader", "_where": "F:\\xt_shipping", "author": {"name": "<PERSON> @jhnns"}, "bugs": {"url": "https://github.com/webpack-contrib/less-loader/issues"}, "bundleDependencies": false, "dependencies": {"clone": "^2.1.1", "loader-utils": "^1.1.0", "pify": "^4.0.1"}, "deprecated": false, "description": "A Less loader for webpack. Compiles Less to CSS.", "devDependencies": {"@babel/cli": "^7.4.4", "@babel/core": "^7.4.4", "@babel/node": "^7.2.2", "@babel/preset-env": "^7.4.4", "@commitlint/cli": "^7.5.2", "@commitlint/config-conventional": "^7.5.0", "@webpack-contrib/defaults": "^4.0.1", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^24.7.1", "commitlint-azure-pipelines-cli": "^1.0.1", "cross-env": "^5.2.0", "del": "^4.1.1", "del-cli": "^1.1.0", "eslint": "^5.16.0", "eslint-plugin-import": "^2.17.2", "eslint-plugin-prettier": "^3.0.1", "husky": "^2.1.0", "inspect-loader": "^1.0.0", "jest": "^24.7.1", "jest-junit": "^6.3.0", "less": "^3.9.0", "lint-staged": "^8.1.5", "memory-fs": "^0.4.1", "prettier": "^1.17.0", "standard-version": "^5.0.2", "webpack": "^4.30.0"}, "engines": {"node": ">= 4.8.0"}, "files": ["dist"], "homepage": "https://github.com/webpack-contrib/less-loader", "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"], "license": "MIT", "main": "dist/cjs.js", "name": "less-loader", "peerDependencies": {"less": "^2.3.1 || ^3.0.0", "webpack": "^2.0.0 || ^3.0.0 || ^4.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/less-loader.git"}, "scripts": {"build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js' --copy-files", "build:fixtures": "babel-node test/helpers/createSpec.js", "clean": "del-cli dist", "commitlint": "commitlint --from=master", "defaults": "webpack-defaults", "lint": "eslint --cache src test", "prebuild": "npm run clean", "prepare": "npm run build", "pretest": "npm run lint", "release": "standard-version", "security": "npm audit", "start": "npm run build -- -w", "test": "cross-env NODE_ENV=test npm run test:coverage", "test:coverage": "cross-env NODE_ENV=test npm run build:fixtures && jest --collectCoverageFrom='src/**/*.js' --coverage", "test:only": "cross-env NODE_ENV=test npm run build:fixtures && jest", "test:watch": "cross-env NODE_ENV=test npm run build:fixtures && jest --watch"}, "version": "5.0.0"}