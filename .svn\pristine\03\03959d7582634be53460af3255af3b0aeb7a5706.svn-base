{"_args": [["browserify-cipher@1.0.1", "F:\\xt_shipping"]], "_development": true, "_from": "browserify-cipher@1.0.1", "_id": "browserify-cipher@1.0.1", "_inBundle": false, "_integrity": "sha512-sPhkz0ARKbf4rRQt2hTpAHqn47X3llLkUGn+xEJzLjwY8LRs2p0v7ljvI5EyoRO/mexrNunNECisZs+gw2zz1w==", "_location": "/browserify-cipher", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "browserify-cipher@1.0.1", "name": "browserify-cipher", "escapedName": "browserify-cipher", "rawSpec": "1.0.1", "saveSpec": null, "fetchSpec": "1.0.1"}, "_requiredBy": ["/crypto-browserify"], "_resolved": "https://registry.npmjs.org/browserify-cipher/-/browserify-cipher-1.0.1.tgz", "_spec": "1.0.1", "_where": "F:\\xt_shipping", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "browser": "browser.js", "bugs": {"url": "https://github.com/crypto-browserify/browserify-cipher/issues"}, "dependencies": {"browserify-aes": "^1.0.4", "browserify-des": "^1.0.0", "evp_bytestokey": "^1.0.0"}, "description": "ciphers for the browser", "devDependencies": {"standard": "^10.0.2", "tap-spec": "^4.1.0", "tape": "^4.2.0"}, "homepage": "https://github.com/crypto-browserify/browserify-cipher#readme", "license": "MIT", "main": "index.js", "name": "browserify-cipher", "repository": {"type": "git", "url": "git+ssh://**************/crypto-browserify/browserify-cipher.git"}, "scripts": {"test": "standard && node test.js | tspec"}, "version": "1.0.1"}