{"_args": [["@webassemblyjs/ast@1.7.11", "F:\\xt_shipping"]], "_development": true, "_from": "@webassemblyjs/ast@1.7.11", "_id": "@webassemblyjs/ast@1.7.11", "_inBundle": false, "_integrity": "sha512-ZEzy4vjvTzScC+SH8RBssQUawpaInUdMTYwYYLh54/s8TuT0gBLuyUnppKsVyZEi876VmmStKsUs28UxPgdvrA==", "_location": "/@webassemblyjs/ast", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@webassemblyjs/ast@1.7.11", "name": "@webassemblyjs/ast", "escapedName": "@webassemblyjs%2fast", "scope": "@webassemblyjs", "rawSpec": "1.7.11", "saveSpec": null, "fetchSpec": "1.7.11"}, "_requiredBy": ["/@vue/cli-plugin-eslint/webpack", "/@webassemblyjs/helper-wasm-section", "/@webassemblyjs/wasm-edit", "/@webassemblyjs/wasm-gen", "/@webassemblyjs/wasm-opt", "/@webassemblyjs/wasm-parser", "/@webassemblyjs/wast-parser", "/@webassemblyjs/wast-printer"], "_resolved": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.7.11.tgz", "_spec": "1.7.11", "_where": "F:\\xt_shipping", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dependencies": {"@webassemblyjs/helper-module-context": "1.7.11", "@webassemblyjs/helper-wasm-bytecode": "1.7.11", "@webassemblyjs/wast-parser": "1.7.11"}, "description": "AST utils for webassemblyjs", "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.11", "array.prototype.flatmap": "^1.2.1", "dump-exports": "^0.1.0", "mamacro": "^0.0.3"}, "gitHead": "4291990bfc4648bc6676091a955d12dc3c7e5909", "homepage": "https://github.com/xtuc/webassemblyjs#readme", "keywords": ["webassembly", "javascript", "ast"], "license": "MIT", "main": "lib/index.js", "module": "esm/index.js", "name": "@webassemblyjs/ast", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/xtuc/webassemblyjs.git"}, "version": "1.7.11"}