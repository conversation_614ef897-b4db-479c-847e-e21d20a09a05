#!/usr/bin/env node

/**
 * 优化的预渲染构建脚本
 * 自动处理npm镜像切换、puppeteer安装和构建过程
 */

'use strict'

const { execSync, spawn } = require('child_process')
const chalk = require('chalk')
const ora = require('ora')
const fs = require('fs')
const path = require('path')

class BuildOptimizer {
  constructor() {
    this.originalRegistry = ''
    this.spinner = null
  }

  // 获取当前npm镜像
  getCurrentRegistry() {
    try {
      return execSync('npm config get registry', { encoding: 'utf8' }).trim()
    } catch (error) {
      console.log(chalk.yellow('Warning: Could not get current npm registry'))
      return 'https://registry.npmjs.org/'
    }
  }

  // 检查是否安装了nrm
  checkNrmInstalled() {
    try {
      execSync('nrm --version', { stdio: 'ignore' })
      return true
    } catch (error) {
      return false
    }
  }

  // 切换到npm官方镜像
  switchToNpmRegistry() {
    this.spinner = ora('切换到npm官方镜像...').start()
    this.originalRegistry = this.getCurrentRegistry()
    
    try {
      if (this.checkNrmInstalled()) {
        // 如果安装了nrm，使用nrm切换
        execSync('nrm use npm', { stdio: 'ignore' })
        this.spinner.succeed('已使用nrm切换到npm官方镜像')
      } else {
        // 直接设置npm registry
        execSync('npm config set registry https://registry.npmjs.org/', { stdio: 'ignore' })
        this.spinner.succeed('已切换到npm官方镜像')
      }
    } catch (error) {
      this.spinner.fail('切换npm镜像失败')
      throw error
    }
  }

  // 恢复原始镜像
  restoreOriginalRegistry() {
    if (this.originalRegistry && this.originalRegistry !== 'https://registry.npmjs.org/') {
      try {
        execSync(`npm config set registry ${this.originalRegistry}`, { stdio: 'ignore' })
        console.log(chalk.green('已恢复原始npm镜像'))
      } catch (error) {
        console.log(chalk.yellow('Warning: Could not restore original npm registry'))
      }
    }
  }

  // 检查puppeteer是否已安装
  checkPuppeteerInstalled() {
    try {
      const packageJson = require('../package.json')
      return packageJson.dependencies && packageJson.dependencies.puppeteer
    } catch (error) {
      return false
    }
  }

  // 设置puppeteer下载镜像
  setPuppeteerMirror() {
    this.spinner = ora('设置puppeteer下载镜像...').start()
    try {
      execSync('npm config set PUPPETEER_DOWNLOAD_HOST=https://npmmirror.com/mirrors', { stdio: 'ignore' })
      this.spinner.succeed('已设置puppeteer下载镜像')
    } catch (error) {
      this.spinner.fail('设置puppeteer镜像失败')
      throw error
    }
  }

  // 安装puppeteer
  installPuppeteer() {
    if (this.checkPuppeteerInstalled()) {
      console.log(chalk.green('puppeteer已安装，跳过安装步骤'))
      return
    }

    this.spinner = ora('安装puppeteer...').start()
    try {
      execSync('npm install --save puppeteer', { stdio: 'pipe' })
      this.spinner.succeed('puppeteer安装成功')
    } catch (error) {
      this.spinner.fail('puppeteer安装失败')
      console.log(chalk.red('尝试使用镜像安装...'))
      
      // 如果安装失败，设置镜像后重试
      this.setPuppeteerMirror()
      this.spinner = ora('使用镜像重新安装puppeteer...').start()
      try {
        execSync('npm install --save puppeteer', { stdio: 'pipe' })
        this.spinner.succeed('puppeteer安装成功')
      } catch (retryError) {
        this.spinner.fail('puppeteer安装失败')
        throw retryError
      }
    }
  }

  // 运行构建
  runBuild() {
    this.spinner = ora('开始构建...').start()
    
    return new Promise((resolve, reject) => {
      const buildProcess = spawn('npm', ['run', 'build'], {
        stdio: ['inherit', 'pipe', 'pipe'],
        shell: true
      })

      let output = ''
      let errorOutput = ''

      buildProcess.stdout.on('data', (data) => {
        output += data.toString()
        // 实时显示构建进度
        process.stdout.write(data)
      })

      buildProcess.stderr.on('data', (data) => {
        errorOutput += data.toString()
        process.stderr.write(data)
      })

      buildProcess.on('close', (code) => {
        this.spinner.stop()
        if (code === 0) {
          console.log(chalk.green('\n✅ 构建成功完成！'))
          resolve()
        } else {
          console.log(chalk.red(`\n❌ 构建失败，退出码: ${code}`))
          reject(new Error(`Build failed with code ${code}`))
        }
      })

      buildProcess.on('error', (error) => {
        this.spinner.fail('构建过程出错')
        reject(error)
      })
    })
  }

  // 主要执行流程
  async execute() {
    console.log(chalk.blue('🚀 开始优化构建流程...\n'))

    try {
      // 1. 切换到npm镜像
      this.switchToNpmRegistry()

      // 2. 安装puppeteer
      this.installPuppeteer()

      // 3. 运行构建
      await this.runBuild()

      console.log(chalk.green('\n🎉 所有步骤完成！'))

    } catch (error) {
      console.error(chalk.red('\n❌ 构建过程中出现错误:'))
      console.error(error.message)
      process.exit(1)
    } finally {
      // 4. 恢复原始镜像
      this.restoreOriginalRegistry()
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const optimizer = new BuildOptimizer()
  optimizer.execute()
}

module.exports = BuildOptimizer
