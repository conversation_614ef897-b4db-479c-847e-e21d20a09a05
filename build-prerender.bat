@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    优化预渲染构建脚本
echo ========================================
echo.

echo [1/4] 检查并切换npm镜像...
call npm config get registry > temp_registry.txt
set /p CURRENT_REGISTRY=<temp_registry.txt
del temp_registry.txt

echo 当前镜像: %CURRENT_REGISTRY%

:: 检查是否安装了nrm
nrm --version >nul 2>&1
if %errorlevel% == 0 (
    echo 使用nrm切换到npm官方镜像...
    call nrm use npm
) else (
    echo 直接设置npm官方镜像...
    call npm config set registry https://registry.npmjs.org/
)

echo.
echo [2/4] 设置puppeteer下载镜像...
call npm config set PUPPETEER_DOWNLOAD_HOST=https://npmmirror.com/mirrors

echo.
echo [3/4] 检查并安装puppeteer...
:: 检查puppeteer是否已安装
npm list puppeteer >nul 2>&1
if %errorlevel% == 0 (
    echo puppeteer已安装，跳过安装步骤
) else (
    echo 安装puppeteer...
    call npm install --save puppeteer
    if %errorlevel% neq 0 (
        echo puppeteer安装失败，请检查网络连接
        pause
        exit /b 1
    )
)

echo.
echo [4/4] 开始构建...
call npm run build

echo.
if %errorlevel% == 0 (
    echo ========================================
    echo    构建成功完成！
    echo ========================================
) else (
    echo ========================================
    echo    构建失败！
    echo ========================================
)

echo.
echo 恢复原始npm镜像...
if not "%CURRENT_REGISTRY%"=="https://registry.npmjs.org/" (
    call npm config set registry %CURRENT_REGISTRY%
    echo 已恢复到: %CURRENT_REGISTRY%
)

echo.
pause
