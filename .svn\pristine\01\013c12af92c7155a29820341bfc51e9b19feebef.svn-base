{"_args": [["v-click-outside-x@3.7.1", "F:\\xt_shipping"]], "_from": "v-click-outside-x@3.7.1", "_id": "v-click-outside-x@3.7.1", "_inBundle": false, "_integrity": "sha512-WmUgmcIXr9clVpm1AYS/FgHtcDicfnfoxgQCNg4O6vfk9GVnxA0vSqO321ogUo0b7czYTidj7fQENvWFMWOkUg==", "_location": "/v-click-outside-x", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "v-click-outside-x@3.7.1", "name": "v-click-outside-x", "escapedName": "v-click-outside-x", "rawSpec": "3.7.1", "saveSpec": null, "fetchSpec": "3.7.1"}, "_requiredBy": ["/iview"], "_resolved": "https://registry.npmjs.org/v-click-outside-x/-/v-click-outside-x-3.7.1.tgz", "_spec": "3.7.1", "_where": "F:\\xt_shipping", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "browser": "dist/v-click-outside-x.min.js", "browserslist": ["> 1%", "Explorer >= 9"], "bugs": {"url": "https://github.com/Xotic750/v-click-outside-x/issues"}, "copyright": "Copyright (c) 2018-present", "dependencies": {}, "description": "Vue directive to react on clicks outside an element.", "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.2.2", "@babel/plugin-transform-property-mutators": "^7.2.0", "@babel/plugin-transform-runtime": "^7.2.0", "@babel/preset-env": "^7.2.3", "@babel/runtime": "^7.2.0", "@prorenata/eslint-config-vue": "^1.11.0", "babel-core": "^7.0.0-0", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.5", "cross-env": "^5.2.0", "eslint": "^5.12.1", "eslint-config-airbnb-base": "^13.1.0", "eslint-config-prettier": "^3.6.0", "eslint-friendly-formatter": "^4.0.1", "eslint-import-resolver-webpack": "^0.10.1", "eslint-loader": "^2.1.1", "eslint-plugin-babel": "^5.3.0", "eslint-plugin-compat": "^2.6.3", "eslint-plugin-css-modules": "^2.11.0", "eslint-plugin-eslint-comments": "^3.0.1", "eslint-plugin-html": "^5.0.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-jest": "^22.1.3", "eslint-plugin-jsdoc": "^4.0.0", "eslint-plugin-json": "^1.3.2", "eslint-plugin-lodash": "^5.1.0", "eslint-plugin-no-use-extend-native": "^0.4.0", "eslint-plugin-prefer-object-spread": "^1.2.1", "eslint-plugin-prettier": "^3.0.1", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-sort-class-members": "^1.4.0", "eslint-plugin-switch-case": "^1.1.2", "eslint-plugin-vue": "^5.1.0", "friendly-errors-webpack-plugin": "^1.7.0", "husky": "^1.3.1", "jest": "^23.6.0", "jest-cli": "^23.6.0", "jest-file": "^1.0.0", "prettier": "^1.14.3", "rimraf": "^2.6.3", "semver": "^5.6.0", "terser-webpack-plugin": "^1.2.1", "webpack": "^4.29.0", "webpack-bundle-analyzer": "^3.0.3", "webpack-cli": "^3.2.1", "webpack-merge": "^4.2.1"}, "engines": {"node": ">=8.11.4", "npm": "6.4.1"}, "homepage": "https://github.com/Xotic750/v-click-outside-x.git", "keywords": ["vue", "click", "outside", "directive"], "license": "MIT", "main": "dist/v-click-outside-x.js", "module": "src/index.js", "name": "v-click-outside-x", "repository": {"type": "git", "url": "git+https://github.com/Xotic750/v-click-outside-x.git"}, "scripts": {"build": "cross-env NODE_ENV=production npm run build:base --", "build:base": "webpack --bail --progress --profile --colors", "build:dev": "npm run build:base --", "clean": "rimraf dist && npm run clean:coverage", "clean:coverage": "rimraf __tests__/coverage", "lint": "eslint -f 'node_modules/eslint-friendly-formatter' --ext .js .", "lint-fix": "npm run lint -- --fix", "precommit": "npm run security fix && npm run build", "prepush": "npm run precommit", "report": "npm run build -- --env.report", "report:dev": "npm run build:dev -- --env.report", "security": "npm audit", "security-fix": "npm run security -- --fix", "start": "nodemon --exec \"npm run build\" --watch src", "test": "npm run clean:coverage && jest", "test:ci": "npm run test -- --ci --maxWorkers=2", "test:coverage": "npm run test -- --coverage"}, "version": "3.7.1"}