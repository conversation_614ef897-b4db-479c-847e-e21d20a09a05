<template>
  <div>
    <h4 class="pageTitl">公司大事件</h4>
    <!-- <template v-for="(item, index) in processList">
      <div class="process-content">
        <h3 class="animated" data-ani="fadeInUp" :data-delay="300 * index">{{ item.durationDate }}</h3>
        <span class="left-top-point"></span>
        <p class="contentR right-desc animated fast" data-ani="slideInRight" :data-delay="80 + 300 * index" v-html="item.desc"></p>
      </div>
    </template> -->
    <div class="process-content">
      <div v-html="desc"></div>
    </div>
  </div>
</template>
<script>
import { getCurList } from '@/api/api'

export default {
  data () {
    return {
      desc: '暂无相关介绍',
      listQuery: {
        pageSize: 10,
        pageNumber: 1,
        category_code: 10002002
      },
      processList: [
        {durationDate: '未来可期……', src: 'static/images/process/2.png', title: '新增最大油化船舶“兴通66”轮投产', desc: '▪ 2021年01月&nbsp;&nbsp;新建11500载重吨双相不锈钢化学品船“兴通739”轮投产'},
        {durationDate: '2018年 - 2020年', src: 'static/images/process/2.png', title: '新增最大油化船舶“兴通66”轮投产', desc: '▪ 2020年11月&nbsp;&nbsp;成品油船“兴通799”轮投入运营(载重吨49962)<br>▪ 2020年11月&nbsp;&nbsp;化学品船“兴通719”轮投入运营(载重吨12498.99)<br>▪ 2020年08月&nbsp;&nbsp;化学品船“兴通789”轮投入运营(载重吨44997.4)<br>▪ 2019年10月&nbsp;&nbsp;新建7490载重吨双相不锈钢化学品船“兴通56”轮投产<br>▪ 2019年01月&nbsp;&nbsp;新建5390载重吨双相不锈钢化学品船“兴通6”轮投产<br>▪ 2018年06月&nbsp;&nbsp;当选为中国船东协会化工品运输专业委员会常委会副主任单位'},
        {durationDate: '2012年 - 2017年', src: 'static/images/process/2.png', title: '新增最大油化船舶“兴通66”轮投产', desc: '▪ 2015年12月&nbsp;&nbsp;新建不锈钢化学品船“兴通16”轮投产<br>▪ 2014年09月&nbsp;&nbsp;新建首艘不锈钢化学品船“兴通19”轮投产<br>▪ 2014年07月&nbsp;&nbsp;新增最大油化船舶“兴通66”轮投产（载重吨11241吨）<br>▪ 2012年12月&nbsp;&nbsp;新建“兴通9”、“兴通10”成品油船投产<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;船舶首次顺利通过BP大石油公司检查<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;被增选为中国船东协会化工品运输专业委员会常委委员<br>▪ 2012年01月&nbsp;&nbsp;创办《兴通人》期刊'},
        {durationDate: '2008年 - 2011年', src: 'static/images/process/3.png', title: '新建“兴通油69”轮建造完工投入营运', desc: '▪ 2011年03月&nbsp;&nbsp;新建“兴通99”轮投入营运<br>▪ 2010年05月&nbsp;&nbsp;新增“兴通89”轮投产<br>▪ 2008年04月&nbsp;&nbsp;新增“兴通油59”轮投入营运<br>▪ 2008年01月&nbsp;&nbsp;新建“兴通油69”轮建造完工投入营运'},
        {durationDate: '1997年 - 2007年', src: 'static/images/process/1.png', title: '1997年12月<br>2000年06月', desc: '▪ 2005年12月&nbsp;&nbsp;正式进驻“兴通海运大厦”办公<br>▪ 2003年10月&nbsp;&nbsp;成立党支部<br>▪ 2002年10月&nbsp;&nbsp;建立安全管理体系<br>▪ 2001年03月&nbsp;&nbsp;筹资投建“兴通海运大厦”（办公楼、员工宿舍楼）<br>▪ 2000年06月&nbsp;&nbsp;经交通部批准，获得国内沿海、长江及珠江水系间的成品油、散装化学品运输的经营资质<br>▪ 1997年12月&nbsp;&nbsp;兴通船务公司正式注册成立，经营国内沿海成品油运输业务及相关代理业务'}
      ]
    }
  },
  methods: {
    getList () {
      getCurList(this.listQuery).then(res => {
        if (res.data.Code === 10000) {
          this.desc = res.data.rows[0].article_content
        } else {
          this.desc = '暂无招聘信息'
          this.$Message.error(res.data.Message)
        }
      })
    }
  },
  created () {
    this.getList()
  }
}
</script>

<style lang="less">
.pageTitl {
  font-size: 35px;
  margin: 10px 0 50px 13%;
  position: relative;
  color: #333;
  font-weight: bold;
}
.process-content {
  color: #546066;
  margin-left: 12%;
  border-left: 4px solid #dcdcdc;
  h3 {
    display: block;
    width: 100%;
    font-weight: 600;
    margin: 20px 0 0 60px;
    color: #2E3035;
  }
  .contentL {
    padding: 21px 75px 15px 25px;
    p {
      font-size: 12px;
      margin-top: 15px;
    }
  }
  .contentR {
    display: inline-block;
    font-size: 14px;
    // padding: 10px 0 45px 60px;
  }
  .left-top-point {
    position: absolute;
    margin-top: -27px;
    margin-left: -13px;
    width: 15px;
    height: 15px;
    background-color: #1981E6;
    border-radius: 50%;
    padding: 10px;
    border: 1px solid #fff;
  }
  .right-desc {
    margin: 0 0 0 60px;
    line-height: 36px;
  }
}
@media (max-width: 992px) {
  .pageTitl {
    margin: 10px 0 35px 30px;
    font-size: 32px;
  }
  .process-content {
    border-left: none;
    margin-bottom: 20px;
    margin-left: 30px;
    h3 {
      margin: 10px 0;
    }
    .contentL {
      padding: 0;
      p {
        display: none;
      }
    }
    .contentR {
      padding: 0;
      .left-top-point {
        display: none;
      }
    }
  }
}
@media (max-width: 767px) {
  .pageTitl {
    margin: 10px 0 35px 0;
    font-size: 26px;
  }
  .process-content {
    margin-left: 0;
    h3 {
      font-size: 20px;
    }
  }
}
</style>
