{"_args": [["filesize@3.6.1", "F:\\xt_shipping"]], "_development": true, "_from": "filesize@3.6.1", "_id": "filesize@3.6.1", "_inBundle": false, "_integrity": "sha512-7KjR1vv6qnicaPMi1iiTcI85CyYwRO/PSFCu6SvqL8jN2Wjt/NIYQTFtFs7fSDCYOstUkEWIQGFUg5YZQfjlcg==", "_location": "/filesize", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "filesize@3.6.1", "name": "filesize", "escapedName": "filesize", "rawSpec": "3.6.1", "saveSpec": null, "fetchSpec": "3.6.1"}, "_requiredBy": ["/webpack-bundle-analyzer"], "_resolved": "https://registry.npmjs.org/filesize/-/filesize-3.6.1.tgz", "_spec": "3.6.1", "_where": "F:\\xt_shipping", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/avoidwork/filesize.js/issues"}, "description": "JavaScript library to generate a human readable String describing the file size", "devDependencies": {"babel-core": "^6.26.0", "babel-minify": "^0.3.0", "babel-preset-env": "^1.6.1", "grunt": "^1.0.1", "grunt-babel": "^7.0.0", "grunt-cli": "^1.2.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-nodeunit": "^1.0.0", "grunt-contrib-uglify": "^3.3.0", "grunt-contrib-watch": "^1.0.0", "grunt-eslint": "^20.1.0"}, "engines": {"node": ">= 0.4.0"}, "homepage": "https://filesizejs.com", "keywords": ["file", "filesize", "size", "readable", "file system", "bytes", "diff"], "license": "BSD-3-<PERSON><PERSON>", "main": "lib/filesize", "name": "filesize", "repository": {"type": "git", "url": "git://github.com/avoidwork/filesize.js.git"}, "scripts": {"test": "grunt test"}, "version": "3.6.1"}