<template>
  <div class="partner-text">
    <div class="col-md-3 col-sm-4 col-xs-3 animated" v-for="(item, index) in images" :key="index" data-ani="zoomIn" :data-delay="100 * index">
      <div class="parter-img-bg">
        <img :src="item.src">
      </div>
      <h3>{{ item.title }}</h3>
      <h4 class="visible-lg" v-html="item.p"></h4>
    </div>
  </div>
</template>
<script>
import config from '@/api/config'
import { getCurArticleList } from '@/api/api'

export default {
  data () {
    return {
      baseImgUrl: '',
      listQuery: {
        category_code: 10002003
      },
      images: [
        // { src: 'static/images/partner/1.png', title: '福建联合石油化工有限公司' },
        // { src: 'static/images/partner/2.png', title: '浙江石油化工有限公司' },
        // { src: 'static/images/partner/3.png', title: '中国海洋石油集团有限公司' },
        // { src: 'static/images/partner/4.png', title: '中石化华东销售分公司', p: '中石化森美（福建）石油有限公司' },
        // { src: 'static/images/partner/5.png', title: '中国航空油料集团有限公司' },
        // { src: 'static/images/partner/6.png', title: '中化泉州石化有限公司' },
        // { src: 'static/images/partner/7.png',  title: '珠海碧辟化工有限公司'},
        // { src: 'static/images/partner/8.png', title: '福建石油化工集团有限责任公司' },
        // { src: 'static/images/partner/9.png', title: '逸盛大化石化有限公司' },
        // { src: 'static/images/partner/10.png', title: '中海壳牌石油化工有限公司' },
        // { src: 'static/images/partner/11.png', title: '荣盛石化股份有限公司' },
        // { src: 'static/images/partner/12.png', title: '泉州市立信化工贸易有限公司' }
      ]
    }
  },
  methods: {
    getList () {
      getCurArticleList(this.listQuery).then(res => {
        if(res.data.Code === 10000) {
          this.images = res.data.Result[0].sub
          res.data.Result[0].sub.map((item, index) => {
            this.images[index].src = this.baseImgUrl + item.article_img
            this.images[index].title = item.article_title
            this.images[index].p = item.article_remark + '<br>'
          })
        } else{
          this.$Message.error(res.data.Message)
        }
      })
    }
  },
  created () {
    this.baseImgUrl = process.env.NODE_ENV === 'development' ? config.imgUrl.dev : config.imgUrl.pro
    this.getList()
  }
}
</script>
<style lang="less" scoped>
.partner-text {
  > div {
    text-align: center;
    margin-bottom: 10px;
    padding: 0 10px;
  }
  .parter-img-bg {
    display: flex;
    width: 100%;
    height: 150px;
    justify-content: center;
    align-items: center;
  }
  img {
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
  }
  h3 {
    font-size: 16px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  h4 {
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
@media only screen and (max-width: 991px) {
  .partner-text h3 {
    font-size: 14px;
  }
}
@media only screen and (max-width: 480px) {
  .partner-text > div {
    width: 50%;
    padding: 0 5px;
  }
}
</style>
