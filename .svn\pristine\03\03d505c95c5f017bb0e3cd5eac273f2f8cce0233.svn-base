<template>
  <!-- center -->
  <div class="center-container">
    <swiper :options="swiperOption" class="swiper-container">
      <!-- 第一屏 -->
      <swiper-slide class="swipe-content swipe-onebackg">
        <div class="text-one text-left">
          <div class="illustration ani slow" data-ani="flipInY">
            <img src="static/images/pro/illustration.png" alt="">
          </div>
          <!-- <div class="qr-code">
            <img :src="codeclick === 1 ? 'static/images/pro/code.png' : 'static/images/pro/u14.jpg'" class="ani" data-ani="fadeInUp">
            <div class="download-btn">
              <Button :type="codeclick === 1 ? 'primary' : 'default'" :style="codeclick === 1 ? 'color: white;' : 'color: #0063C2;'"
                icon="logo-apple" @click="changecode(1)">iPhone版下载</Button>
              <Button :type="codeclick === 2 ? 'primary' : 'default'" :style="codeclick === 2 ? 'color: white' : 'color: #0063C2;border: 1px solid #0063C2'"
                icon="logo-android" @click="changecode(2)">Android版下载</Button>
            </div>
          </div> -->
        </div>
        <div class="oneimg ani" data-ani="zoomIn"><img src="static/images/pro/oneimgs.png" alt=""></div>
      </swiper-slide>
      <!-- 船位查询 -->
      <!-- <swiper-slide class="swipe-content swipe-twobackg">
        <div class="position-content">
          <h3 class="pro_title ani" data-ani="fadeInLeft">船位查询<span>01</span></h3>
          <p class="ani" data-ani="fadeInLeft" data-delay="200">提供1000多艘<br>油化品船舶的<br>AIS精准定位查询。</p>
          <p class="content-img text-twoimg ani" data-ani="fadeInRight"><img src="static/images/pro/03.png" alt=""></p>
        </div>
      </swiper-slide>
      <!// 找船找货
      <swiper-slide class="swipe-content swipe-threebackg">
        <div class="position-content text-right">
          <h3 class="pro_title ani" data-ani="fadeInLeft">找船找货<span>02</span></h3>
          <p class="ani" data-ani="fadeInLeft" data-delay="200">提供船期货盘、<br>船东货主找船找货更便利。</p>
          <p class="content-img text-threeimg ani" data-ani="fadeInRight"><img src="static/images/pro/1.png" alt=""></p>
          <a href="http://www.xtshipping.net/shipDate/list.html" target="_blank" class="seemore ani" data-ani="fadeInUp">了解更多</a>
        </div>
      </swiper-slide>
      <!// 船代委托
      <swiper-slide class="swipe-content swipe-fourbackg">
        <div class="position-content">
          <h3 class="pro_title ani" data-ani="fadeInLeft">船代委托<span>03</span></h3>
          <p class="ani" data-ani="fadeInLeft" data-delay="200">更智能、便捷的<br>船舶代理服务。<br>航次动态实时推送。</p>
          <p class="content-img text-twoimg ani" data-ani="fadeInRight"><img src="static/images/pro/04.png" alt=""></p>
        </div>
      </swiper-slide>
      <!// 企业定制
      <swiper-slide class="swipe-content swipe-fivebackg">
        <div class="position-content text-right">
          <h3 class="pro_title ani" data-ani="fadeInLeft">企业定制<span>04</span></h3>
          <p class="ani" data-ani="fadeInLeft" data-delay="200">提供船期货盘、<br>船东货主找船找货更便利。</p>
          <p class="content-img text-threeimg ani" data-ani="fadeInRight"><img src="static/images/pro/05.png" alt=""></p>
        </div>
      </swiper-slide>
      <!// 航运工具
      <swiper-slide class="swipe-content swipe-fourbackg other-part" style="background-color:#f2f7fa;">
        <div class="position-content">
          <div class="hidden_bg_t"></div>
          <h3 class="pro_title ani" data-ani="fadeInLeft">航运工具<span>05</span></h3>
          <p class="ani" data-ani="fadeInLeft" data-delay="200">提供便捷的<br>航运工具查询服务。</p>
          <p class="content-img text-twoimg ani" data-ani="fadeInRight"><img src="static/images/pro/2.png" alt=""></p>
          <a href="http://www.xtshipping.net/tool-tide.html" target="_blank" class="seemore ani" data-ani="fadeInUp">了解更多</a>
        </div>
      </swiper-slide>
      <!// 航运资讯
      <swiper-slide class="swipe-content swipe-fivebackg swipe-sixbackg">
        <div class="position-content text-right">
          <h3 class="pro_title ani" data-ani="fadeInLeft">航运资讯<span>06</span></h3>
          <p class="ani" data-ani="fadeInLeft" data-delay="200">丰富的航运资讯、<br>热门航运头条。</p>
          <p class="content-img text-threeimg ani" data-ani="fadeInRight"><img src="static/images/pro/3.png" alt=""></p>
          <a href="http://www.xtshipping.net/view/cmsArticle/cmsArticleList?alias=hyzs&currPage=0" target="_blank" class="seemore ani" data-ani="fadeInUp">了解更多</a>
        </div>
      </swiper-slide>
      <!// 物料商城
      <swiper-slide class="swipe-content swipe-sevenbackg">
        <div class="position-content">
          <div class="hidden_bg_t"></div>
          <h3 class="pro_title ani" data-ani="fadeInLeft">物料商城<span>07</span></h3>
          <p class="ani" data-ani="fadeInLeft" data-delay="200">船舶物料网上商城，<br>优质商家、厂家入驻<br>并提供丰富的产品服务</p>
          <p class="content-img text-sevenimg ani" data-ani="fadeInRight"><img src="static/images/pro/4.png" alt=""></p>
          <a href="http://mall.xtshipping.net/" target="_blank" class="seemore ani" data-ani="fadeInUp">了解更多</a>
        </div>
      </swiper-slide>
      <!// 海上应急
      <swiper-slide class="swipe-content swipe-eightbackg">
        <div class="position-content text-right">
          <div class="hidden_bg_o"></div>
          <h3 class="pro_title ani" data-ani="fadeInLeft">海上应急<span>08</span></h3>
          <p class="ani" data-ani="fadeInLeft" data-delay="200">为湄洲湾港口<br>提供丰富的应急资源，<br>包括专家库、应急设备库等。</p>
          <p class="content-img text-eightimg ani" data-ani="fadeInRight"><img src="static/images/pro/5.png" alt=""></p>
          <a href="http://www.xtshipping.net/emergency" target="_blank" class="seemore ani" data-ani="fadeInUp">了解更多</a>
        </div>
      </swiper-slide>
      <div class="swiper-pagination" slot="pagination"></div> -->
    </swiper>
  </div>
</template>

<script>
import { swiper, swiperSlide } from "vue-awesome-swiper";
export default {
  name: "carrousel",
  components: {
    swiper,
    swiperSlide
  },
  data() {
    const swiperAnimateCache = a => {
      for (var j = 0; j < a.slides.length; j++) {
        let allBoxes = a.slides[j].querySelectorAll(".ani");
        for (let i = 0; i < allBoxes.length; i++) {
          allBoxes[i].attributes["style"]
            ? allBoxes[i].setAttribute(
                "swiper-animate-style-cache",
                allBoxes[i].attributes["style"].value
              )
            : allBoxes[i].setAttribute("swiper-animate-style-cache", " "),
            (allBoxes[i].style.visibility = "hidden");
        }
      }
    };
    const swiperAnimate = a => {
      clearSwiperAnimate(a);
      var b = a.slides[a.activeIndex].querySelectorAll(".ani");
      for (var i = 0; i < b.length; i++) {
        b[i].style.visibility = "visible";
        let effect = b[i].dataset["ani"];
        b[i].className = b[i].className + " " + effect + " " + "animated";
        let style = b[i].attributes["style"].value;
        let duration = b[i].dataset["duration"] || "100";
        duration &&
          (style =
            style +
            "animation-duration:" +
            duration +
            ";-webkit-animation-duration:" +
            duration +
            ";");
        let delay = b[i].dataset["delay"] || "0";
        delay &&
          (style =
            style +
            "animation-delay:" +
            delay +
            "ms;-webkit-animation-delay:" +
            delay +
            "ms;");
        b[i].setAttribute("style", style);
      }
    };
    const clearSwiperAnimate = a => {
      for (let j = 0; j < a.slides.length; j++) {
        let allBoxes = a.slides[j].querySelectorAll(".ani");
        for (let i = 0; i < allBoxes.length; i++) {
          allBoxes[i].attributes["swiper-animate-style-cache"] &&
            allBoxes[i].setAttribute(
              "style",
              allBoxes[i].attributes["swiper-animate-style-cache"].value
            );
          allBoxes[i].style.visibility = "hidden";
          allBoxes[i].className = allBoxes[i].className.replace(
            "animated",
            " "
          );
          if (allBoxes[i].dataset["ani"] && allBoxes[i].dataset["ani"] !== "") {
            allBoxes[i].classList.remove(allBoxes[i].dataset["ani"]);
          }
        }
      }
    };
    return {
      // swiper滑动设置
      swiperOption: {
        direction: "vertical",
        slidesPerView: 1,
        spaceBetween: 30,
        speed: 700, //限制滚轴时间间隔
        observer: true, //修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, //修改swiper的父元素时，自动初始化swiper
        paginationClickable: true,
        autoHeight: true, // 自适应高度
        height: window.innerHeight - $("#appheader").height(),
        // loop: true,
        autoplay: {
          delay: 999994000,
          disableOnInteraction: false // 移动端触屏滑动
        },
        mousewheel: true,
        pagination: {
          el: ".swiper-pagination",
          clickable: true
        },
        /* 初始化animate */
        on: {
          init: function() {
            swiperAnimate(this);
          },
          slideChange: function() {
            swiperAnimateCache(this);
            swiperAnimate(this);
          }
        }
      },
      codeclick: 1 // 版本下载,1是iphone,2是android
    };
  },
  methods: {
    // 版本下载切换，1是iphone,2是android
    changecode(d) {
      if (d === 1) {
        this.codeclick = 1;
      } else {
        this.codeclick = 2;
      }
    }
  }
};
</script>
<style scoped>
@import url("swiper/dist/css/swiper.min.css");
@import url("../../../static/css/pro.css");
</style>
