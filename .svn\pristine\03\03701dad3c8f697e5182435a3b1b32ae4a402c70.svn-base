{"_args": [["spdx-license-ids@3.0.5", "F:\\xt_shipping"]], "_development": true, "_from": "spdx-license-ids@3.0.5", "_id": "spdx-license-ids@3.0.5", "_inBundle": false, "_integrity": "sha512-J+FWzZoynJEXGphVIS+XEh3kFSjZX/1i9gFBaWQcB+/tmpe2qUsSBABpcxqxnAxFdiUFEgAX1bjYGQvIZmoz9Q==", "_location": "/spdx-license-ids", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "spdx-license-ids@3.0.5", "name": "spdx-license-ids", "escapedName": "spdx-license-ids", "rawSpec": "3.0.5", "saveSpec": null, "fetchSpec": "3.0.5"}, "_requiredBy": ["/spdx-correct", "/spdx-expression-parse"], "_resolved": "https://registry.npmjs.org/spdx-license-ids/-/spdx-license-ids-3.0.5.tgz", "_spec": "3.0.5", "_where": "F:\\xt_shipping", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/shinnn"}, "bugs": {"url": "https://github.com/shinnn/spdx-license-ids/issues"}, "description": "A list of SPDX license identifiers", "devDependencies": {"@shinnn/eslint-config": "^6.8.7", "chalk": "^2.4.1", "eslint": "^5.10.0", "get-spdx-license-ids": "^2.1.0", "rmfr": "^2.0.0", "tape": "^4.9.1"}, "eslintConfig": {"extends": "@shinnn"}, "files": ["deprecated.json", "index.json"], "homepage": "https://github.com/shinnn/spdx-license-ids#readme", "keywords": ["spdx", "license", "licenses", "id", "identifier", "identifiers", "json", "array", "oss"], "license": "CC0-1.0", "name": "spdx-license-ids", "repository": {"type": "git", "url": "git+https://github.com/shinnn/spdx-license-ids.git"}, "scripts": {"build": "node build.js", "pretest": "eslint .", "test": "node test.js"}, "version": "3.0.5"}