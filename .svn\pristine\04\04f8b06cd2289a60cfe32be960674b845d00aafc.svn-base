{"_args": [["supports-color@6.1.0", "F:\\xt_shipping"]], "_development": true, "_from": "supports-color@6.1.0", "_id": "supports-color@6.1.0", "_inBundle": false, "_integrity": "sha512-qe1jfm1Mg7Nq/NSh6XE24gPXROEVsWHxC1LIx//XNlD9iw7YZQGjZNjYN7xGaEG6iKdA8EtNFW6R0gjnVXp+wQ==", "_location": "/cssnano-preset-default/supports-color", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "supports-color@6.1.0", "name": "supports-color", "escapedName": "supports-color", "rawSpec": "6.1.0", "saveSpec": null, "fetchSpec": "6.1.0"}, "_requiredBy": ["/cssnano-preset-default/postcss"], "_resolved": "https://registry.npmjs.org/supports-color/-/supports-color-6.1.0.tgz", "_spec": "6.1.0", "_where": "F:\\xt_shipping", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "browser": "browser.js", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dependencies": {"has-flag": "^3.0.0"}, "description": "Detect whether a terminal supports color", "devDependencies": {"ava": "^0.25.0", "import-fresh": "^2.0.0", "xo": "^0.23.0"}, "engines": {"node": ">=6"}, "files": ["index.js", "browser.js"], "homepage": "https://github.com/chalk/supports-color#readme", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "license": "MIT", "name": "supports-color", "repository": {"type": "git", "url": "git+https://github.com/chalk/supports-color.git"}, "scripts": {"test": "xo && ava"}, "version": "6.1.0"}