<template>
  <!-- center -->
  <div class="center-container container">
    <leftNav :nav="navData" @nav-back="navBack"></leftNav>
    <div class="center_content col-md-12 col-sm-12 col-lg-9">
      <breadcrumb :breadcrumb="breadcrumbData"></breadcrumb>
      <!-- 新闻详情页 -->
      <div class="news-content">
        <h3>{{ detailList.title }}</h3>
        <p>{{ detailList.time }}</p>
        <div v-html="detailList.content"></div>
      </div>
    </div>
  </div>
</template>

<script>
import config from '@/api/config'
import { getCurList } from '@/api/api'
import leftNav from "@/components/leftNav"
import breadcrumb from "@/components/breadcrumbComponent"
import layoutGlobal from '@/assets/js/layoutGlobal.js' // 全局共用布局组件
export default {
  mixins: [layoutGlobal],
  name: "news",
  components: {
    leftNav,
    breadcrumb
  },
  data() {
    return {
      detailList: {},
      listQuery: {
        pageSize: 8,
        pageNumber: 1,
        category_code: 10003001
      },
      breadcrumbData: [
        {
            name: '新闻中心',
            path: '/news'
        }
      ], // 面包屑数据
      navData: [
        {
          title: "新闻中心",
          name: "公司动态",
          path: "/news?id=1"
        },
        {
          title: "新闻中心",
          name: "基层信息",
          path: "/news?id=2"
        },
        {
          title: "新闻中心",
          name: "行业资讯",
          path: "/news?id=3"
        }
      ]
    };
  },
  methods: {
    // 左侧栏回调
    navBack (d) {
      this.detailList = {}
      this.currentView = 'aboutComponent' + d.id
    }
  },
  created() {
    // 设置banner图片
    let bannerArr = [
      {
        src: "static/images/banner/newbanner.png"
      }
    ]
    this.$store.commit("setBannerObj", bannerArr)
    // 设置详情数据
    let _detailObj = JSON.parse(window.localStorage.newsObj) // this.$store.state.curNewsDetail
    this.detailList = {
      content: _detailObj.content_detail,
      title: _detailObj.title,
      time: _detailObj.time
    }
  }
};
</script>
<style scoped>
.center-container {
  display: flex;
}
.news_img {
  padding-right: 20px;
}
.news_title {
  font-size: 18px;
  font-weight: bold;
  color: #000;
  margin-bottom: 15px;
}
.news-content h3 {
  font-size: 24px;
  font-weight: 600;
  text-align: center;
}
.news-content p {
  text-align: center;
  font-size: 16px;
  margin-top: 10px;
  margin-bottom: 20px;
}
.news_time {
  color:#1981E6;
}
</style>

