# 预渲染构建优化方案

## 问题描述

由于项目使用了 `prerender-spa-plugin` 进行SEO优化，每次构建时都需要手动执行以下步骤：

1. 切换到npm镜像（例：`nrm use npm`）
2. `npm i --save puppeteer`
3. 如果步骤2失败，需要设置镜像：`npm config set PUPPETEER_DOWNLOAD_HOST=https://npmmirror.com/mirrors`
4. `npm run build`

## 优化方案

我们提供了三种自动化解决方案：

### 方案一：使用优化的npm脚本（推荐）

```bash
npm run build:optimized
```

或者

```bash
npm run build:prerender
```

这个脚本会自动：
- 检测并切换到npm官方镜像
- 检查puppeteer是否已安装，如未安装则自动安装
- 如果安装失败，自动设置镜像后重试
- 执行构建
- 构建完成后恢复原始npm镜像

### 方案二：使用批处理脚本（Windows用户）

双击运行 `build-prerender.bat` 文件，或在命令行中执行：

```cmd
build-prerender.bat
```

### 方案三：手动使用Node.js脚本

```bash
node scripts/build-with-prerender.js
```

## 配置优化

### Webpack配置优化

我们对 `build/webpack.prod.conf.js` 中的 PrerenderSPAPlugin 配置进行了优化：

- 启用无头模式运行
- 添加puppeteer启动参数优化
- 设置单进程模式减少内存使用
- 增加超时时间到30秒
- 限制并发渲染路由数量

### Puppeteer配置优化

添加了以下启动参数：
- `--no-sandbox`: 禁用沙盒模式
- `--disable-setuid-sandbox`: 禁用setuid沙盒
- `--disable-dev-shm-usage`: 禁用/dev/shm使用
- `--single-process`: 单进程模式
- `--disable-gpu`: 禁用GPU加速

## 使用说明

### 首次使用

1. 确保已安装Node.js和npm
2. 在项目根目录运行：
   ```bash
   npm run build:optimized
   ```

### 日常使用

直接运行优化脚本即可：
```bash
npm run build:prerender
```

### 故障排除

如果遇到问题：

1. **puppeteer安装失败**
   - 脚本会自动设置镜像重试
   - 手动设置：`npm config set PUPPETEER_DOWNLOAD_HOST=https://npmmirror.com/mirrors`

2. **构建内存不足**
   - 已优化为单进程模式
   - 如仍有问题，可以增加Node.js内存限制：
     ```bash
     node --max-old-space-size=4096 scripts/build-with-prerender.js
     ```

3. **网络问题**
   - 脚本会自动切换到npm官方镜像
   - 如需使用其他镜像，可在构建前手动设置

## 文件说明

- `scripts/build-with-prerender.js`: Node.js优化构建脚本
- `build-prerender.bat`: Windows批处理脚本
- `BUILD_OPTIMIZATION.md`: 本说明文档

## 注意事项

1. 脚本会临时切换npm镜像，构建完成后自动恢复
2. puppeteer只会在未安装时才进行安装
3. 所有操作都有详细的日志输出
4. 支持中断和错误恢复

## 性能提升

使用优化脚本后：
- 减少手动操作步骤
- 自动处理常见错误
- 提高构建成功率
- 节省开发时间
